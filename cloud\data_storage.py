"""
Data Storage Service for IoT Smart Irrigation System
Handles time-series data storage, metadata management, and data retrieval
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
import pymongo
from pymongo import MongoClient
import redis
from influxdb_client import InfluxDBClient, Point
from influxdb_client.client.write_api import SYNCHRONOUS

logger = logging.getLogger(__name__)


@dataclass
class StorageConfig:
    """Configuration for data storage services"""
    # MongoDB configuration
    mongodb_url: str = "mongodb://localhost:27017"
    mongodb_database: str = "irrigation_system"
    
    # InfluxDB configuration
    influxdb_url: str = "http://localhost:8086"
    influxdb_token: str = "your-token-here"
    influxdb_org: str = "irrigation-org"
    influxdb_bucket: str = "sensor-data"
    
    # Redis configuration
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    
    # Data retention policies
    raw_data_retention_days: int = 90
    aggregated_data_retention_days: int = 365
    cache_ttl_seconds: int = 300


class TimeSeriesStorage:
    """Handles time-series sensor data storage using InfluxDB"""
    
    def __init__(self, config: StorageConfig):
        self.config = config
        self.client = None
        self.write_api = None
        self.query_api = None
        
    async def connect(self):
        """Connect to InfluxDB"""
        try:
            self.client = InfluxDBClient(
                url=self.config.influxdb_url,
                token=self.config.influxdb_token,
                org=self.config.influxdb_org
            )
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
            self.query_api = self.client.query_api()
            
            # Test connection
            health = self.client.health()
            if health.status == "pass":
                logger.info("Connected to InfluxDB successfully")
            else:
                logger.error("InfluxDB health check failed")
                
        except Exception as e:
            logger.error(f"Failed to connect to InfluxDB: {e}")
            raise
    
    async def store_sensor_reading(self, sensor_data: Dict[str, Any]) -> bool:
        """Store sensor reading in time-series database"""
        try:
            # Create InfluxDB point
            point = Point("sensor_reading") \
                .tag("sensor_id", sensor_data.get("sensor_id")) \
                .tag("sensor_type", sensor_data.get("sensor_type")) \
                .tag("farm_id", sensor_data.get("farm_id", "default")) \
                .tag("field_id", sensor_data.get("field_id", "field_01")) \
                .tag("zone_id", sensor_data.get("zone_id", "unknown"))
            
            # Add sensor value
            if "value" in sensor_data:
                point = point.field("value", float(sensor_data["value"]))
            
            # Add quality score
            if "quality_score" in sensor_data:
                point = point.field("quality_score", float(sensor_data["quality_score"]))
            
            # Add metadata fields
            metadata = sensor_data.get("metadata", {})
            for key, value in metadata.items():
                if isinstance(value, (int, float)):
                    point = point.field(f"meta_{key}", float(value))
                elif isinstance(value, str):
                    point = point.tag(f"meta_{key}", value)
            
            # Set timestamp
            if "timestamp" in sensor_data:
                timestamp = datetime.fromisoformat(sensor_data["timestamp"].replace('Z', '+00:00'))
                point = point.time(timestamp)
            
            # Write to InfluxDB
            self.write_api.write(bucket=self.config.influxdb_bucket, record=point)
            return True
            
        except Exception as e:
            logger.error(f"Error storing sensor reading: {e}")
            return False
    
    async def query_sensor_data(self, 
                               sensor_id: str = None,
                               sensor_type: str = None,
                               start_time: datetime = None,
                               end_time: datetime = None,
                               limit: int = 1000) -> List[Dict[str, Any]]:
        """Query sensor data from time-series database"""
        try:
            # Build query
            query_parts = [f'from(bucket: "{self.config.influxdb_bucket}")']
            
            # Time range
            if start_time:
                start_str = start_time.isoformat() + "Z"
                query_parts.append(f'|> range(start: {start_str}')
                if end_time:
                    end_str = end_time.isoformat() + "Z"
                    query_parts[-1] += f', stop: {end_str}'
                query_parts[-1] += ')'
            else:
                query_parts.append('|> range(start: -24h)')
            
            # Filters
            filters = []
            if sensor_id:
                filters.append(f'r.sensor_id == "{sensor_id}"')
            if sensor_type:
                filters.append(f'r.sensor_type == "{sensor_type}"')
            
            if filters:
                query_parts.append(f'|> filter(fn: (r) => {" and ".join(filters)})')
            
            # Limit
            query_parts.append(f'|> limit(n: {limit})')
            
            query = ' '.join(query_parts)
            
            # Execute query
            result = self.query_api.query(query)
            
            # Process results
            data = []
            for table in result:
                for record in table.records:
                    data.append({
                        "timestamp": record.get_time().isoformat(),
                        "sensor_id": record.values.get("sensor_id"),
                        "sensor_type": record.values.get("sensor_type"),
                        "value": record.get_value(),
                        "field": record.get_field(),
                        **{k: v for k, v in record.values.items() if k.startswith("meta_")}
                    })
            
            return data
            
        except Exception as e:
            logger.error(f"Error querying sensor data: {e}")
            return []
    
    async def get_aggregated_data(self,
                                 sensor_id: str,
                                 aggregation: str = "mean",
                                 window: str = "1h",
                                 start_time: datetime = None,
                                 end_time: datetime = None) -> List[Dict[str, Any]]:
        """Get aggregated sensor data"""
        try:
            # Build aggregation query
            query_parts = [f'from(bucket: "{self.config.influxdb_bucket}")']
            
            # Time range
            if start_time:
                start_str = start_time.isoformat() + "Z"
                query_parts.append(f'|> range(start: {start_str}')
                if end_time:
                    end_str = end_time.isoformat() + "Z"
                    query_parts[-1] += f', stop: {end_str}'
                query_parts[-1] += ')'
            else:
                query_parts.append('|> range(start: -7d)')
            
            # Filter by sensor
            query_parts.append(f'|> filter(fn: (r) => r.sensor_id == "{sensor_id}")')
            query_parts.append(f'|> filter(fn: (r) => r._field == "value")')
            
            # Aggregation
            query_parts.append(f'|> aggregateWindow(every: {window}, fn: {aggregation})')
            
            query = ' '.join(query_parts)
            
            # Execute query
            result = self.query_api.query(query)
            
            # Process results
            data = []
            for table in result:
                for record in table.records:
                    data.append({
                        "timestamp": record.get_time().isoformat(),
                        "value": record.get_value(),
                        "aggregation": aggregation,
                        "window": window
                    })
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting aggregated data: {e}")
            return []


class MetadataStorage:
    """Handles metadata storage using MongoDB"""
    
    def __init__(self, config: StorageConfig):
        self.config = config
        self.client = None
        self.db = None
        
    async def connect(self):
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(self.config.mongodb_url)
            self.db = self.client[self.config.mongodb_database]
            
            # Test connection
            self.client.admin.command('ping')
            logger.info("Connected to MongoDB successfully")
            
            # Create indexes
            await self._create_indexes()
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    async def _create_indexes(self):
        """Create database indexes for performance"""
        try:
            # Sensor metadata indexes
            self.db.sensors.create_index("sensor_id", unique=True)
            self.db.sensors.create_index([("farm_id", 1), ("field_id", 1)])
            
            # Device metadata indexes
            self.db.devices.create_index("device_id", unique=True)
            self.db.devices.create_index("device_type")
            
            # Alert indexes
            self.db.alerts.create_index([("timestamp", -1)])
            self.db.alerts.create_index("severity")
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
    
    async def store_sensor_metadata(self, sensor_metadata: Dict[str, Any]) -> bool:
        """Store sensor metadata"""
        try:
            sensor_metadata["updated_at"] = datetime.now()
            
            result = self.db.sensors.update_one(
                {"sensor_id": sensor_metadata["sensor_id"]},
                {"$set": sensor_metadata},
                upsert=True
            )
            
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"Error storing sensor metadata: {e}")
            return False
    
    async def get_sensor_metadata(self, sensor_id: str) -> Optional[Dict[str, Any]]:
        """Get sensor metadata"""
        try:
            result = self.db.sensors.find_one({"sensor_id": sensor_id})
            if result:
                result.pop("_id", None)  # Remove MongoDB ObjectId
            return result
            
        except Exception as e:
            logger.error(f"Error getting sensor metadata: {e}")
            return None
    
    async def store_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Store alert/notification"""
        try:
            alert_data["created_at"] = datetime.now()
            alert_data["acknowledged"] = False
            
            result = self.db.alerts.insert_one(alert_data)
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"Error storing alert: {e}")
            return False
    
    async def get_active_alerts(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get active alerts"""
        try:
            cursor = self.db.alerts.find(
                {"acknowledged": False}
            ).sort("timestamp", -1).limit(limit)
            
            alerts = []
            for alert in cursor:
                alert.pop("_id", None)
                alerts.append(alert)
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error getting active alerts: {e}")
            return []


class CacheStorage:
    """Handles caching using Redis"""
    
    def __init__(self, config: StorageConfig):
        self.config = config
        self.client = None
        
    async def connect(self):
        """Connect to Redis"""
        try:
            self.client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                decode_responses=True
            )
            
            # Test connection
            self.client.ping()
            logger.info("Connected to Redis successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def cache_sensor_data(self, sensor_id: str, data: Dict[str, Any], ttl: int = None) -> bool:
        """Cache sensor data"""
        try:
            ttl = ttl or self.config.cache_ttl_seconds
            key = f"sensor:{sensor_id}:latest"
            
            self.client.setex(key, ttl, json.dumps(data, default=str))
            return True
            
        except Exception as e:
            logger.error(f"Error caching sensor data: {e}")
            return False
    
    async def get_cached_sensor_data(self, sensor_id: str) -> Optional[Dict[str, Any]]:
        """Get cached sensor data"""
        try:
            key = f"sensor:{sensor_id}:latest"
            data = self.client.get(key)
            
            if data:
                return json.loads(data)
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached sensor data: {e}")
            return None
    
    async def cache_aggregated_data(self, cache_key: str, data: List[Dict[str, Any]], ttl: int = 3600) -> bool:
        """Cache aggregated data"""
        try:
            self.client.setex(cache_key, ttl, json.dumps(data, default=str))
            return True
            
        except Exception as e:
            logger.error(f"Error caching aggregated data: {e}")
            return False


class DataStorageManager:
    """Manages all data storage operations"""
    
    def __init__(self, config: StorageConfig):
        self.config = config
        self.timeseries = TimeSeriesStorage(config)
        self.metadata = MetadataStorage(config)
        self.cache = CacheStorage(config)
        
    async def initialize(self):
        """Initialize all storage connections"""
        await self.timeseries.connect()
        await self.metadata.connect()
        await self.cache.connect()
        logger.info("Data storage manager initialized successfully")
    
    async def store_processed_data(self, processed_data: Dict[str, Any]) -> bool:
        """Store processed sensor data across all storage systems"""
        try:
            # Store in time-series database
            ts_success = await self.timeseries.store_sensor_reading(processed_data)
            
            # Cache latest reading
            if "reading" in processed_data:
                reading_data = processed_data["reading"]
                if hasattr(reading_data, '__dict__'):
                    reading_dict = asdict(reading_data)
                else:
                    reading_dict = reading_data
                
                cache_success = await self.cache.cache_sensor_data(
                    reading_dict.get("sensor_id", "unknown"),
                    reading_dict
                )
            
            # Store metadata if new sensor
            if "reading" in processed_data:
                reading = processed_data["reading"]
                sensor_id = reading.sensor_id if hasattr(reading, 'sensor_id') else reading.get("sensor_id")
                
                if sensor_id:
                    existing_metadata = await self.metadata.get_sensor_metadata(sensor_id)
                    if not existing_metadata:
                        metadata = {
                            "sensor_id": sensor_id,
                            "sensor_type": reading.sensor_type if hasattr(reading, 'sensor_type') else reading.get("sensor_type"),
                            "location": reading.location if hasattr(reading, 'location') else reading.get("location"),
                            "unit": reading.unit if hasattr(reading, 'unit') else reading.get("unit"),
                            "first_seen": datetime.now()
                        }
                        await self.metadata.store_sensor_metadata(metadata)
            
            return ts_success
            
        except Exception as e:
            logger.error(f"Error storing processed data: {e}")
            return False
