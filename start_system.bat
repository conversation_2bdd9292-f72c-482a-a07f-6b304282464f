@echo off
echo 🌱 IoT Smart Irrigation System - Windows Startup
echo ================================================

echo.
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Trying 'py' command...
    py --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Python is not installed or not in PATH
        echo Please install Python 3.11+ from https://python.org
        echo Make sure to check "Add Python to PATH" during installation
        pause
        exit /b 1
    ) else (
        echo ✅ Python found using 'py' command
        set PYTHON_CMD=py
    )
) else (
    echo ✅ Python found using 'python' command
    set PYTHON_CMD=python
)

echo.
echo Installing required packages...
%PYTHON_CMD% -m pip install --upgrade pip
%PYTHON_CMD% -m pip install fastapi uvicorn paho-mqtt numpy pandas scikit-learn matplotlib plotly pydantic asyncio-mqtt aiofiles

echo.
echo Creating necessary directories...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "config" mkdir config

echo.
echo 🚀 Starting IoT Smart Irrigation System...
echo.
echo Access points will be:
echo   • Main Dashboard: http://localhost:8000
echo   • Mobile Interface: http://localhost:8000/mobile/mobile_app.html
echo   • API Documentation: http://localhost:8000/docs
echo.
echo Default login: admin / admin123
echo.
echo Press Ctrl+C to stop the system
echo.

%PYTHON_CMD% main.py

pause
