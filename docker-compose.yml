version: '3.8'

services:
  # MQTT Broker
  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: irrigation_mqtt
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./deployment/mosquitto/config:/mosquitto/config
      - ./deployment/mosquitto/data:/mosquitto/data
      - ./deployment/mosquitto/log:/mosquitto/log
    restart: unless-stopped
    networks:
      - irrigation_network

  # MongoDB for metadata storage
  mongodb:
    image: mongo:6.0
    container_name: irrigation_mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: irrigation_admin
      MONGO_INITDB_ROOT_PASSWORD: secure_password_123
      MONGO_INITDB_DATABASE: irrigation_system
    volumes:
      - mongodb_data:/data/db
      - ./deployment/mongodb/init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - irrigation_network

  # InfluxDB for time-series data
  influxdb:
    image: influxdb:2.7
    container_name: irrigation_influxdb
    ports:
      - "8086:8086"
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: irrigation_admin
      DOCKER_INFLUXDB_INIT_PASSWORD: secure_password_123
      DOCKER_INFLUXDB_INIT_ORG: irrigation-org
      DOCKER_INFLUXDB_INIT_BUCKET: sensor-data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: irrigation-super-secret-auth-token
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    restart: unless-stopped
    networks:
      - irrigation_network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: irrigation_redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis_password_123
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - irrigation_network

  # Main IoT Application
  irrigation_app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: irrigation_main_app
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - MQTT_BROKER=mosquitto
      - MONGODB_URL=******************************************************************************
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=irrigation-super-secret-auth-token
      - INFLUXDB_ORG=irrigation-org
      - INFLUXDB_BUCKET=sensor-data
      - REDIS_HOST=redis
      - REDIS_PASSWORD=redis_password_123
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - mosquitto
      - mongodb
      - influxdb
      - redis
    restart: unless-stopped
    networks:
      - irrigation_network

  # Grafana for advanced visualization
  grafana:
    image: grafana/grafana:10.0.0
    container_name: irrigation_grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: grafana_admin_123
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/grafana/provisioning:/etc/grafana/provisioning
      - ./deployment/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - influxdb
    restart: unless-stopped
    networks:
      - irrigation_network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: irrigation_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - irrigation_app
      - grafana
    restart: unless-stopped
    networks:
      - irrigation_network

  # Node Exporter for system monitoring
  node_exporter:
    image: prom/node-exporter:latest
    container_name: irrigation_node_exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped
    networks:
      - irrigation_network

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: irrigation_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - irrigation_network

volumes:
  mongodb_data:
    driver: local
  influxdb_data:
    driver: local
  influxdb_config:
    driver: local
  redis_data:
    driver: local
  grafana_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  irrigation_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
