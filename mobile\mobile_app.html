<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Irrigation Mobile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 20px;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            opacity: 0.9;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .main-content {
            padding: 20px;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #3498db;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        
        .zones-section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .zone-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .zone-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .zone-name {
            font-weight: bold;
            font-size: 1.1rem;
            color: #2c3e50;
        }
        
        .zone-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-idle {
            background: #f39c12;
            color: white;
        }
        
        .status-irrigating {
            background: #27ae60;
            color: white;
        }
        
        .status-error {
            background: #e74c3c;
            color: white;
        }
        
        .zone-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .metric {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .metric-value {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 2px;
        }
        
        .metric-label {
            font-size: 0.8rem;
            color: #7f8c8d;
        }
        
        .zone-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn:active {
            transform: scale(0.95);
        }
        
        .alerts-section {
            margin-bottom: 25px;
        }
        
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #f39c12;
        }
        
        .alert-high {
            background: #f8d7da;
            border-color: #f5c6cb;
            border-left-color: #e74c3c;
        }
        
        .alert-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .alert-time {
            font-size: 0.8rem;
            color: #7f8c8d;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
        }
        
        .nav-item {
            text-align: center;
            padding: 10px;
            color: #7f8c8d;
            text-decoration: none;
            font-size: 0.8rem;
            transition: color 0.3s ease;
        }
        
        .nav-item.active {
            color: #3498db;
        }
        
        .nav-icon {
            font-size: 1.2rem;
            margin-bottom: 2px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .refresh-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
        }
        
        .moisture-gauge {
            position: relative;
            width: 60px;
            height: 60px;
            margin: 0 auto;
        }
        
        .gauge-bg {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #e74c3c 0%, #f39c12 30%, #27ae60 70%, #3498db 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .gauge-inner {
            width: 80%;
            height: 80%;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="refresh-btn" onclick="refreshData()">🔄</button>
            <h1>🌱 Smart Irrigation</h1>
            <div class="status-bar">
                <div class="connection-status">
                    <div class="status-dot"></div>
                    <span>Connected</span>
                </div>
                <div id="last-update">Just now</div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="quick-stats">
                <div class="stat-card">
                    <div class="stat-value" id="active-zones">-</div>
                    <div class="stat-label">Active Zones</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="water-today">-</div>
                    <div class="stat-label">Water Today (L)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="avg-moisture">-</div>
                    <div class="stat-label">Avg Moisture</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="system-health">-</div>
                    <div class="stat-label">System Health</div>
                </div>
            </div>
            
            <div class="zones-section">
                <div class="section-title">Irrigation Zones</div>
                <div id="zones-container" class="loading">Loading zones...</div>
            </div>
            
            <div class="alerts-section">
                <div class="section-title">Recent Alerts</div>
                <div id="alerts-container" class="loading">Loading alerts...</div>
            </div>
        </div>
        
        <div class="bottom-nav">
            <a href="#" class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div>Home</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">📊</div>
                <div>Analytics</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">⚙️</div>
                <div>Settings</div>
            </a>
            <a href="#" class="nav-item">
                <div class="nav-icon">🔔</div>
                <div>Alerts</div>
            </a>
        </div>
    </div>
    
    <script>
        // Mobile app functionality
        let wsConnection = null;
        let lastUpdateTime = new Date();
        
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            connectWebSocket();
            loadInitialData();
            
            // Update time display
            setInterval(updateTimeDisplay, 1000);
            
            // Auto-refresh data every 30 seconds
            setInterval(refreshData, 30000);
        });
        
        function initializeApp() {
            console.log('Smart Irrigation Mobile App initialized');
            
            // Add touch feedback for buttons
            document.addEventListener('touchstart', function(e) {
                if (e.target.classList.contains('btn')) {
                    e.target.style.transform = 'scale(0.95)';
                }
            });
            
            document.addEventListener('touchend', function(e) {
                if (e.target.classList.contains('btn')) {
                    setTimeout(() => {
                        e.target.style.transform = 'scale(1)';
                    }, 100);
                }
            });
        }
        
        function connectWebSocket() {
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;
                
                wsConnection = new WebSocket(wsUrl);
                
                wsConnection.onopen = function() {
                    console.log('WebSocket connected');
                    updateConnectionStatus(true);
                };
                
                wsConnection.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleRealtimeUpdate(message);
                    lastUpdateTime = new Date();
                };
                
                wsConnection.onclose = function() {
                    console.log('WebSocket disconnected');
                    updateConnectionStatus(false);
                    
                    // Attempt to reconnect after 5 seconds
                    setTimeout(connectWebSocket, 5000);
                };
                
                wsConnection.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    updateConnectionStatus(false);
                };
                
            } catch (error) {
                console.error('Failed to connect WebSocket:', error);
                updateConnectionStatus(false);
            }
        }
        
        function updateConnectionStatus(connected) {
            const statusDot = document.querySelector('.status-dot');
            const statusText = document.querySelector('.connection-status span');
            
            if (connected) {
                statusDot.style.background = '#27ae60';
                statusText.textContent = 'Connected';
            } else {
                statusDot.style.background = '#e74c3c';
                statusText.textContent = 'Disconnected';
            }
        }
        
        function handleRealtimeUpdate(message) {
            console.log('Real-time update:', message);
            
            if (message.type === 'sensor_data') {
                updateSensorData(message.data);
            } else if (message.type === 'system_event') {
                addAlert(message.data);
            } else if (message.type === 'zone_status') {
                updateZoneStatus(message.data);
            }
        }
        
        async function loadInitialData() {
            try {
                // Load system status
                const statusResponse = await fetch('/api/status');
                const statusData = await statusResponse.json();
                updateQuickStats(statusData);
                
                // Load zones
                const zonesResponse = await fetch('/api/zones');
                const zonesData = await zonesResponse.json();
                updateZonesDisplay(zonesData.zones);
                
                // Load alerts
                const alertsResponse = await fetch('/api/alerts');
                const alertsData = await alertsResponse.json();
                updateAlertsDisplay(alertsData.alerts);
                
            } catch (error) {
                console.error('Error loading initial data:', error);
                showError('Failed to load data. Please check your connection.');
            }
        }
        
        function updateQuickStats(data) {
            document.getElementById('active-zones').textContent = data.system.active_irrigations || 0;
            document.getElementById('water-today').textContent = '245'; // Mock data
            document.getElementById('avg-moisture').textContent = '52%'; // Mock data
            document.getElementById('system-health').textContent = data.system.is_running ? '98%' : '0%';
        }
        
        function updateZonesDisplay(zones) {
            const container = document.getElementById('zones-container');
            
            if (!zones || zones.length === 0) {
                container.innerHTML = '<div class="loading">No zones available</div>';
                return;
            }
            
            container.innerHTML = zones.map(zone => `
                <div class="zone-card">
                    <div class="zone-header">
                        <div class="zone-name">${zone.name}</div>
                        <div class="zone-status status-${zone.status}">${zone.status}</div>
                    </div>
                    
                    <div class="zone-metrics">
                        <div class="metric">
                            <div class="moisture-gauge">
                                <div class="gauge-bg">
                                    <div class="gauge-inner">${zone.current_moisture.toFixed(0)}%</div>
                                </div>
                            </div>
                            <div class="metric-label">Moisture</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">25°C</div>
                            <div class="metric-label">Temperature</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">65%</div>
                            <div class="metric-label">Humidity</div>
                        </div>
                    </div>
                    
                    <div class="zone-controls">
                        <button class="btn btn-success" onclick="startIrrigation('${zone.zone_id}')">
                            💧 Start
                        </button>
                        <button class="btn btn-danger" onclick="stopIrrigation('${zone.zone_id}')">
                            ⏹️ Stop
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        function updateAlertsDisplay(alerts) {
            const container = document.getElementById('alerts-container');
            
            if (!alerts || alerts.length === 0) {
                container.innerHTML = '<div class="loading">No recent alerts</div>';
                return;
            }
            
            const recentAlerts = alerts.slice(-3).reverse();
            
            container.innerHTML = recentAlerts.map(alert => `
                <div class="alert ${alert.severity === 'high' ? 'alert-high' : ''}">
                    <div class="alert-title">${alert.type || 'System Alert'}</div>
                    <div>${alert.message}</div>
                    <div class="alert-time">${new Date(alert.timestamp).toLocaleTimeString()}</div>
                </div>
            `).join('');
        }
        
        async function startIrrigation(zoneId) {
            try {
                const response = await fetch(`/api/zones/${zoneId}/irrigate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        zone_id: zoneId, 
                        action: 'start', 
                        duration_minutes: 30 
                    })
                });
                
                if (response.ok) {
                    showSuccess('Irrigation started successfully');
                    setTimeout(refreshData, 1000);
                } else {
                    showError('Failed to start irrigation');
                }
            } catch (error) {
                console.error('Error starting irrigation:', error);
                showError('Network error. Please try again.');
            }
        }
        
        async function stopIrrigation(zoneId) {
            try {
                const response = await fetch(`/api/zones/${zoneId}/irrigate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        zone_id: zoneId, 
                        action: 'stop' 
                    })
                });
                
                if (response.ok) {
                    showSuccess('Irrigation stopped successfully');
                    setTimeout(refreshData, 1000);
                } else {
                    showError('Failed to stop irrigation');
                }
            } catch (error) {
                console.error('Error stopping irrigation:', error);
                showError('Network error. Please try again.');
            }
        }
        
        function refreshData() {
            console.log('Refreshing data...');
            loadInitialData();
            
            // Visual feedback
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.style.transform = 'rotate(360deg)';
            refreshBtn.style.transition = 'transform 0.5s ease';
            
            setTimeout(() => {
                refreshBtn.style.transform = 'rotate(0deg)';
            }, 500);
        }
        
        function updateTimeDisplay() {
            const now = new Date();
            const timeDiff = Math.floor((now - lastUpdateTime) / 1000);
            
            let timeText;
            if (timeDiff < 60) {
                timeText = 'Just now';
            } else if (timeDiff < 3600) {
                timeText = `${Math.floor(timeDiff / 60)}m ago`;
            } else {
                timeText = `${Math.floor(timeDiff / 3600)}h ago`;
            }
            
            document.getElementById('last-update').textContent = timeText;
        }
        
        function showSuccess(message) {
            showToast(message, 'success');
        }
        
        function showError(message) {
            showToast(message, 'error');
        }
        
        function showToast(message, type) {
            // Create toast notification
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#27ae60' : '#e74c3c'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 1000;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                animation: slideDown 0.3s ease;
            `;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideUp 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
            @keyframes slideUp {
                from { transform: translateX(-50%) translateY(0); opacity: 1; }
                to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        // Handle navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all items
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                
                // Add active class to clicked item
                this.classList.add('active');
                
                // Handle navigation (placeholder for future implementation)
                const navText = this.querySelector('div:last-child').textContent;
                console.log(`Navigating to: ${navText}`);
            });
        });
    </script>
</body>
</html>
