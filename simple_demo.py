"""
IoT Smart Irrigation System - Simplified Demo
A lightweight version that runs without external dependencies
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any
import random
import math

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SimpleSensor:
    """Simplified sensor simulation"""
    
    def __init__(self, sensor_id: str, sensor_type: str, location: Dict[str, float]):
        self.sensor_id = sensor_id
        self.sensor_type = sensor_type
        self.location = location
        self.is_active = True
        self.battery_level = random.uniform(70, 100)
        self.reading_count = 0
        
    def read(self) -> Dict[str, Any]:
        """Generate simulated sensor reading"""
        if not self.is_active:
            return {"error": "Sensor inactive"}
        
        self.reading_count += 1
        self.battery_level = max(0, self.battery_level - random.uniform(0.01, 0.05))
        
        # Generate realistic values based on sensor type
        if "moisture" in self.sensor_type.lower():
            # Soil moisture: 0-100%
            base_value = 45 + 15 * math.sin(time.time() / 3600)  # Hourly variation
            noise = random.uniform(-5, 5)
            value = max(0, min(100, base_value + noise))
            unit = "%"
        elif "temperature" in self.sensor_type.lower():
            # Temperature: 15-35°C with daily variation
            base_temp = 25 + 8 * math.sin(time.time() / 43200)  # Daily variation
            noise = random.uniform(-2, 2)
            value = base_temp + noise
            unit = "°C"
        elif "humidity" in self.sensor_type.lower():
            # Humidity: 30-90%
            base_humidity = 60 + 20 * math.sin(time.time() / 21600)  # 6-hour variation
            noise = random.uniform(-5, 5)
            value = max(30, min(90, base_humidity + noise))
            unit = "%"
        elif "ph" in self.sensor_type.lower():
            # pH: 6.0-8.0
            base_ph = 6.8 + 0.3 * math.sin(time.time() / 86400)  # Daily variation
            noise = random.uniform(-0.1, 0.1)
            value = max(6.0, min(8.0, base_ph + noise))
            unit = "pH"
        else:
            # Generic sensor
            value = random.uniform(0, 100)
            unit = "units"
        
        return {
            "sensor_id": self.sensor_id,
            "sensor_type": self.sensor_type,
            "value": round(value, 2),
            "unit": unit,
            "timestamp": datetime.now().isoformat(),
            "location": self.location,
            "battery_level": round(self.battery_level, 1),
            "reading_count": self.reading_count,
            "quality_score": random.uniform(0.85, 1.0)
        }


class SimpleIrrigationZone:
    """Simplified irrigation zone"""
    
    def __init__(self, zone_id: str, name: str):
        self.zone_id = zone_id
        self.name = name
        self.status = "idle"  # idle, irrigating
        self.current_moisture = random.uniform(40, 60)
        self.target_moisture_min = 35.0
        self.target_moisture_max = 65.0
        self.last_irrigation = None
        self.irrigation_start_time = None
        self.total_water_used = 0.0
        
    def needs_irrigation(self) -> bool:
        return self.current_moisture < self.target_moisture_min
    
    def start_irrigation(self, duration_minutes: int = 30):
        """Start irrigation"""
        if self.status == "irrigating":
            return False
        
        self.status = "irrigating"
        self.irrigation_start_time = datetime.now()
        logger.info(f"🚿 Started irrigation for {self.name} ({duration_minutes} minutes)")
        
        # Schedule automatic stop
        asyncio.create_task(self._auto_stop_irrigation(duration_minutes))
        return True
    
    async def _auto_stop_irrigation(self, duration_minutes: int):
        """Automatically stop irrigation after duration"""
        await asyncio.sleep(duration_minutes * 60)
        self.stop_irrigation()
    
    def stop_irrigation(self):
        """Stop irrigation"""
        if self.status != "irrigating":
            return False
        
        if self.irrigation_start_time:
            duration_hours = (datetime.now() - self.irrigation_start_time).total_seconds() / 3600
            water_used = duration_hours * 300  # 300 L/hour
            self.total_water_used += water_used
            
            # Simulate moisture increase
            self.current_moisture = min(100, self.current_moisture + random.uniform(10, 25))
        
        self.status = "idle"
        self.last_irrigation = datetime.now()
        self.irrigation_start_time = None
        
        logger.info(f"⏹️ Stopped irrigation for {self.name}")
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """Get zone status"""
        return {
            "zone_id": self.zone_id,
            "name": self.name,
            "status": self.status,
            "current_moisture": round(self.current_moisture, 1),
            "target_moisture_min": self.target_moisture_min,
            "target_moisture_max": self.target_moisture_max,
            "last_irrigation": self.last_irrigation.isoformat() if self.last_irrigation else None,
            "total_water_used": round(self.total_water_used, 1),
            "needs_irrigation": self.needs_irrigation()
        }


class SimpleIrrigationSystem:
    """Simplified IoT irrigation system"""
    
    def __init__(self):
        self.sensors = {}
        self.zones = {}
        self.is_running = False
        self.mode = "automatic"
        self.reading_interval = 30  # seconds
        self.startup_time = None
        
        # Statistics
        self.stats = {
            "total_readings": 0,
            "irrigation_events": 0,
            "system_uptime": 0
        }
        
        self._setup_system()
    
    def _setup_system(self):
        """Setup sensors and zones"""
        
        # Create irrigation zones
        zones_config = [
            {"zone_id": "zone_north", "name": "North Field"},
            {"zone_id": "zone_south", "name": "South Field"},
            {"zone_id": "zone_east", "name": "East Field"}
        ]
        
        for zone_config in zones_config:
            zone = SimpleIrrigationZone(zone_config["zone_id"], zone_config["name"])
            self.zones[zone.zone_id] = zone
        
        # Create sensors for each zone
        for zone_id in self.zones.keys():
            # Soil moisture sensor
            moisture_sensor = SimpleSensor(
                sensor_id=f"{zone_id}_moisture",
                sensor_type="SoilMoistureSensor",
                location={"lat": 40.7128 + random.uniform(-0.01, 0.01), 
                         "lng": -74.0060 + random.uniform(-0.01, 0.01)}
            )
            self.sensors[moisture_sensor.sensor_id] = moisture_sensor
            
            # Environmental sensor
            env_sensor = SimpleSensor(
                sensor_id=f"{zone_id}_environment",
                sensor_type="EnvironmentalSensor",
                location=moisture_sensor.location
            )
            self.sensors[env_sensor.sensor_id] = env_sensor
        
        # Weather station sensors
        weather_sensors = [
            ("weather_temperature", "TemperatureSensor"),
            ("weather_humidity", "HumiditySensor"),
            ("weather_ph", "PHSensor")
        ]
        
        for sensor_id, sensor_type in weather_sensors:
            sensor = SimpleSensor(
                sensor_id=sensor_id,
                sensor_type=sensor_type,
                location={"lat": 40.7128, "lng": -74.0060}
            )
            self.sensors[sensor_id] = sensor
        
        logger.info(f"System initialized with {len(self.sensors)} sensors and {len(self.zones)} zones")
    
    async def start_monitoring(self):
        """Start system monitoring"""
        if self.is_running:
            return
        
        self.is_running = True
        self.startup_time = datetime.now()
        
        logger.info("🚀 Starting IoT Smart Irrigation System monitoring...")
        
        try:
            while self.is_running:
                await self._monitoring_cycle()
                await asyncio.sleep(self.reading_interval)
        except KeyboardInterrupt:
            logger.info("System interrupted by user")
        finally:
            self.is_running = False
    
    async def _monitoring_cycle(self):
        """Single monitoring cycle"""
        
        # Collect sensor data
        sensor_data = {}
        for sensor_id, sensor in self.sensors.items():
            try:
                reading = sensor.read()
                sensor_data[sensor_id] = reading
                self.stats["total_readings"] += 1
                
                # Update zone moisture if it's a moisture sensor
                if "moisture" in sensor_id and "value" in reading:
                    zone_id = sensor_id.replace("_moisture", "")
                    if zone_id in self.zones:
                        # Simulate gradual moisture decrease
                        self.zones[zone_id].current_moisture = max(0, 
                            self.zones[zone_id].current_moisture - random.uniform(0.1, 0.5))
                        
            except Exception as e:
                logger.error(f"Error reading sensor {sensor_id}: {e}")
        
        # Make irrigation decisions (simplified)
        if self.mode == "automatic":
            await self._make_irrigation_decisions()
        
        # Log system status
        await self._log_system_status(sensor_data)
    
    async def _make_irrigation_decisions(self):
        """Make automatic irrigation decisions"""
        for zone in self.zones.values():
            if zone.needs_irrigation() and zone.status == "idle":
                # Check if enough time has passed since last irrigation
                if (zone.last_irrigation is None or 
                    (datetime.now() - zone.last_irrigation).total_seconds() > 3600):  # 1 hour minimum
                    
                    duration = random.randint(15, 45)  # 15-45 minutes
                    zone.start_irrigation(duration)
                    self.stats["irrigation_events"] += 1
                    
                    logger.info(f"🤖 Automatic irrigation decision: Start {zone.name} for {duration} minutes")
    
    async def _log_system_status(self, sensor_data: Dict[str, Any]):
        """Log comprehensive system status"""
        if self.stats["total_readings"] % 10 == 0:  # Every 10 readings
            uptime = (datetime.now() - self.startup_time).total_seconds() / 3600 if self.startup_time else 0
            
            active_sensors = len([s for s in self.sensors.values() if s.is_active])
            active_irrigations = len([z for z in self.zones.values() if z.status == "irrigating"])
            
            logger.info("📊 SYSTEM STATUS:")
            logger.info(f"   ⏱️  Uptime: {uptime:.1f} hours")
            logger.info(f"   📡 Sensors: {active_sensors}/{len(self.sensors)} active")
            logger.info(f"   📈 Total readings: {self.stats['total_readings']}")
            logger.info(f"   🚿 Active irrigations: {active_irrigations}")
            logger.info(f"   💧 Irrigation events: {self.stats['irrigation_events']}")
            
            # Show zone status
            for zone in self.zones.values():
                status_emoji = "🚿" if zone.status == "irrigating" else "💤"
                logger.info(f"   {status_emoji} {zone.name}: {zone.current_moisture:.1f}% moisture, {zone.status}")
    
    def manual_start_irrigation(self, zone_id: str, duration: int = 30) -> bool:
        """Manually start irrigation"""
        if zone_id not in self.zones:
            return False
        
        result = self.zones[zone_id].start_irrigation(duration)
        if result:
            self.stats["irrigation_events"] += 1
            logger.info(f"👤 Manual irrigation started: {zone_id} for {duration} minutes")
        
        return result
    
    def manual_stop_irrigation(self, zone_id: str) -> bool:
        """Manually stop irrigation"""
        if zone_id not in self.zones:
            return False
        
        result = self.zones[zone_id].stop_irrigation()
        if result:
            logger.info(f"👤 Manual irrigation stopped: {zone_id}")
        
        return result
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        uptime = (datetime.now() - self.startup_time).total_seconds() if self.startup_time else 0
        
        return {
            "is_running": self.is_running,
            "mode": self.mode,
            "uptime_seconds": uptime,
            "total_sensors": len(self.sensors),
            "active_sensors": len([s for s in self.sensors.values() if s.is_active]),
            "total_zones": len(self.zones),
            "active_irrigations": len([z for z in self.zones.values() if z.status == "irrigating"]),
            "statistics": self.stats,
            "zones": {zone_id: zone.get_status() for zone_id, zone in self.zones.items()}
        }
    
    def stop_monitoring(self):
        """Stop system monitoring"""
        self.is_running = False
        logger.info("🛑 System monitoring stopped")


async def run_demo():
    """Run the simplified demo"""
    print("🌱 IoT Smart Irrigation System - Simplified Demo")
    print("=" * 50)
    print("This demo simulates a complete IoT irrigation system with:")
    print("• Realistic sensor data generation")
    print("• Automatic irrigation control")
    print("• Real-time monitoring and logging")
    print("• Manual control capabilities")
    print("=" * 50)
    print()
    print("The system will run for 5 minutes. Press Ctrl+C to stop early.")
    print()
    
    # Create and start system
    system = SimpleIrrigationSystem()
    
    # Start monitoring in background
    monitoring_task = asyncio.create_task(system.start_monitoring())
    
    # Run for 5 minutes or until interrupted
    try:
        await asyncio.sleep(300)  # 5 minutes
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    
    # Stop system
    system.stop_monitoring()
    
    # Wait for monitoring to stop
    try:
        await asyncio.wait_for(monitoring_task, timeout=5.0)
    except asyncio.TimeoutError:
        monitoring_task.cancel()
    
    # Final status
    print("\n" + "=" * 50)
    print("📊 FINAL SYSTEM STATUS:")
    status = system.get_system_status()
    print(f"   ⏱️  Total uptime: {status['uptime_seconds']/3600:.1f} hours")
    print(f"   📡 Sensor readings: {status['statistics']['total_readings']}")
    print(f"   🚿 Irrigation events: {status['statistics']['irrigation_events']}")
    print("\n   Zone Status:")
    for zone_id, zone_status in status['zones'].items():
        print(f"     • {zone_status['name']}: {zone_status['current_moisture']:.1f}% moisture")
        print(f"       Water used: {zone_status['total_water_used']:.1f}L")
    print("=" * 50)
    print("Demo completed! Thank you for trying the IoT Smart Irrigation System.")


if __name__ == "__main__":
    try:
        asyncio.run(run_demo())
    except KeyboardInterrupt:
        print("\nDemo stopped by user")
    except Exception as e:
        print(f"Demo error: {e}")
    
    input("\nPress Enter to exit...")
