"""
FastAPI Web Dashboard for IoT Smart Irrigation System
Provides REST API and WebSocket endpoints for real-time monitoring
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import uvicorn

# Import our IoT components
from sensors.sensor_simulator import SensorNetwork
from control.irrigation_controller import IrrigationController, IrrigationZone, IrrigationMode
from communication.mqtt_client import MQTTClient

logger = logging.getLogger(__name__)


# Pydantic models for API
class IrrigationCommand(BaseModel):
    zone_id: str
    action: str  # "start" or "stop"
    duration_minutes: Optional[int] = 30


class SystemConfig(BaseModel):
    mode: str
    reading_interval: Optional[int] = 30


class AlertResponse(BaseModel):
    id: str
    type: str
    message: str
    severity: str
    timestamp: str
    acknowledged: bool


class DashboardAPI:
    """FastAPI application for IoT irrigation dashboard"""
    
    def __init__(self):
        self.app = FastAPI(
            title="IoT Smart Irrigation Dashboard",
            description="Real-time monitoring and control for smart irrigation systems",
            version="1.0.0"
        )
        
        # Configure CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # WebSocket connections
        self.websocket_connections: List[WebSocket] = []
        
        # System components (will be injected)
        self.sensor_network: Optional[SensorNetwork] = None
        self.irrigation_controller: Optional[IrrigationController] = None
        self.mqtt_client: Optional[MQTTClient] = None
        
        # Setup routes
        self._setup_routes()
        
        # Mock data for demo
        self.alerts = []
        self.system_events = []
        
    def set_components(self, sensor_network, irrigation_controller, mqtt_client=None):
        """Inject system components"""
        self.sensor_network = sensor_network
        self.irrigation_controller = irrigation_controller
        self.mqtt_client = mqtt_client
        
        # Set up data callback for real-time updates
        if self.sensor_network:
            self.sensor_network.set_data_callback(self._broadcast_sensor_data)
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home():
            """Serve dashboard HTML"""
            return self._get_dashboard_html()
        
        @self.app.get("/api/status")
        async def get_system_status():
            """Get overall system status"""
            if not self.irrigation_controller:
                raise HTTPException(status_code=503, detail="System not initialized")
            
            system_status = self.irrigation_controller.get_system_status()
            network_stats = self.sensor_network.get_network_stats() if self.sensor_network else {}
            
            return {
                "timestamp": datetime.now().isoformat(),
                "system": system_status,
                "network": network_stats,
                "mqtt_connected": self.mqtt_client.is_connected if self.mqtt_client else False
            }
        
        @self.app.get("/api/zones")
        async def get_zones():
            """Get all irrigation zones"""
            if not self.irrigation_controller:
                raise HTTPException(status_code=503, detail="System not initialized")
            
            zones = []
            for zone_id in self.irrigation_controller.zones:
                zone_status = self.irrigation_controller.get_zone_status(zone_id)
                zones.append(zone_status)
            
            return {"zones": zones}
        
        @self.app.get("/api/zones/{zone_id}")
        async def get_zone_details(zone_id: str):
            """Get detailed zone information"""
            if not self.irrigation_controller:
                raise HTTPException(status_code=503, detail="System not initialized")
            
            zone_status = self.irrigation_controller.get_zone_status(zone_id)
            if "error" in zone_status:
                raise HTTPException(status_code=404, detail="Zone not found")
            
            # Get zone sensor data
            if self.sensor_network:
                zone_summary = self.sensor_network.get_zone_summary(zone_id)
                zone_status["sensors"] = zone_summary.get("sensors", {})
            
            return zone_status
        
        @self.app.post("/api/zones/{zone_id}/irrigate")
        async def control_irrigation(zone_id: str, command: IrrigationCommand):
            """Control irrigation for a zone"""
            if not self.irrigation_controller:
                raise HTTPException(status_code=503, detail="System not initialized")
            
            try:
                if command.action == "start":
                    duration = command.duration_minutes or 30
                    success = await self.irrigation_controller.manual_start_irrigation(zone_id, duration)
                    if not success:
                        raise HTTPException(status_code=400, detail="Failed to start irrigation")
                    
                    # Log event
                    self._add_system_event(f"Manual irrigation started for {zone_id} ({duration} min)")
                    
                elif command.action == "stop":
                    success = await self.irrigation_controller.manual_stop_irrigation(zone_id)
                    if not success:
                        raise HTTPException(status_code=400, detail="Failed to stop irrigation")
                    
                    # Log event
                    self._add_system_event(f"Manual irrigation stopped for {zone_id}")
                
                else:
                    raise HTTPException(status_code=400, detail="Invalid action")
                
                return {"success": True, "message": f"Irrigation {command.action} command executed"}
                
            except Exception as e:
                logger.error(f"Error controlling irrigation: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/sensors")
        async def get_sensor_data():
            """Get latest sensor data"""
            if not self.sensor_network:
                raise HTTPException(status_code=503, detail="Sensor network not initialized")
            
            sensor_status = self.sensor_network.get_sensor_status()
            network_stats = self.sensor_network.get_network_stats()
            
            return {
                "sensors": sensor_status,
                "network_stats": network_stats,
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/api/sensors/{sensor_id}/history")
        async def get_sensor_history(sensor_id: str, hours: int = 24):
            """Get sensor data history"""
            # Mock historical data for demo
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # Generate mock historical data
            history = []
            current_time = start_time
            while current_time <= end_time:
                # Simulate realistic data patterns
                base_value = 45.0  # Base moisture level
                daily_variation = 10 * (1 + 0.3 * (current_time.hour - 12) / 12)
                noise = (hash(str(current_time)) % 100) / 100 * 5 - 2.5
                
                history.append({
                    "timestamp": current_time.isoformat(),
                    "value": max(0, min(100, base_value + daily_variation + noise)),
                    "quality_score": 0.9 + (hash(str(current_time)) % 20) / 200
                })
                
                current_time += timedelta(minutes=30)
            
            return {
                "sensor_id": sensor_id,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "data_points": len(history),
                "history": history
            }
        
        @self.app.get("/api/alerts")
        async def get_alerts():
            """Get system alerts"""
            return {"alerts": self.alerts}
        
        @self.app.post("/api/alerts/{alert_id}/acknowledge")
        async def acknowledge_alert(alert_id: str):
            """Acknowledge an alert"""
            for alert in self.alerts:
                if alert["id"] == alert_id:
                    alert["acknowledged"] = True
                    alert["acknowledged_at"] = datetime.now().isoformat()
                    return {"success": True}
            
            raise HTTPException(status_code=404, detail="Alert not found")
        
        @self.app.get("/api/decisions")
        async def get_irrigation_decisions():
            """Get recent irrigation decisions"""
            if not self.irrigation_controller:
                raise HTTPException(status_code=503, detail="System not initialized")
            
            decisions = self.irrigation_controller.get_recent_decisions(limit=50)
            return {"decisions": decisions}
        
        @self.app.post("/api/system/mode")
        async def set_system_mode(config: SystemConfig):
            """Set system operation mode"""
            if not self.irrigation_controller:
                raise HTTPException(status_code=503, detail="System not initialized")
            
            try:
                mode = IrrigationMode(config.mode)
                self.irrigation_controller.set_mode(mode)
                
                # Update sensor reading interval if provided
                if config.reading_interval and self.sensor_network:
                    self.sensor_network.set_reading_interval(config.reading_interval)
                
                self._add_system_event(f"System mode changed to {config.mode}")
                
                return {"success": True, "mode": config.mode}
                
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid mode")
        
        @self.app.get("/api/events")
        async def get_system_events():
            """Get recent system events"""
            return {"events": self.system_events[-100:]}  # Last 100 events
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates"""
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                # Send initial status
                if self.irrigation_controller:
                    status = self.irrigation_controller.get_system_status()
                    await websocket.send_json({
                        "type": "system_status",
                        "data": status,
                        "timestamp": datetime.now().isoformat()
                    })
                
                # Keep connection alive
                while True:
                    await websocket.receive_text()
                    
            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                if websocket in self.websocket_connections:
                    self.websocket_connections.remove(websocket)
    
    async def _broadcast_sensor_data(self, sensor_data: Dict[str, Any]):
        """Broadcast sensor data to WebSocket clients"""
        if not self.websocket_connections:
            return
        
        message = {
            "type": "sensor_data",
            "data": sensor_data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to all connected clients
        disconnected = []
        for websocket in self.websocket_connections:
            try:
                await websocket.send_json(message)
            except Exception:
                disconnected.append(websocket)
        
        # Remove disconnected clients
        for ws in disconnected:
            self.websocket_connections.remove(ws)
    
    def _add_system_event(self, message: str, event_type: str = "info"):
        """Add system event to log"""
        event = {
            "id": f"event_{len(self.system_events)}",
            "type": event_type,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.system_events.append(event)
        
        # Broadcast to WebSocket clients
        asyncio.create_task(self._broadcast_event(event))
    
    async def _broadcast_event(self, event: Dict[str, Any]):
        """Broadcast system event to WebSocket clients"""
        if not self.websocket_connections:
            return
        
        message = {
            "type": "system_event",
            "data": event,
            "timestamp": datetime.now().isoformat()
        }
        
        disconnected = []
        for websocket in self.websocket_connections:
            try:
                await websocket.send_json(message)
            except Exception:
                disconnected.append(websocket)
        
        for ws in disconnected:
            self.websocket_connections.remove(ws)
    
    def _get_dashboard_html(self) -> str:
        """Return dashboard HTML"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>IoT Smart Irrigation Dashboard</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
                .status-online { background: #27ae60; }
                .status-offline { background: #e74c3c; }
                .status-warning { background: #f39c12; }
                .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 4px; }
                .btn-primary { background: #3498db; color: white; }
                .btn-success { background: #27ae60; color: white; }
                .btn-danger { background: #e74c3c; color: white; }
                .metric { display: flex; justify-content: space-between; margin: 8px 0; }
                .loading { text-align: center; color: #7f8c8d; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🌱 IoT Smart Irrigation Dashboard</h1>
                <p>Real-time monitoring and control for smart irrigation systems</p>
            </div>
            
            <div class="dashboard">
                <div class="card">
                    <h3>System Status</h3>
                    <div id="system-status" class="loading">Loading...</div>
                </div>
                
                <div class="card">
                    <h3>Irrigation Zones</h3>
                    <div id="zones-status" class="loading">Loading...</div>
                </div>
                
                <div class="card">
                    <h3>Sensor Network</h3>
                    <div id="sensor-status" class="loading">Loading...</div>
                </div>
                
                <div class="card">
                    <h3>Recent Events</h3>
                    <div id="recent-events" class="loading">Loading...</div>
                </div>
            </div>
            
            <script>
                // WebSocket connection for real-time updates
                const ws = new WebSocket('ws://localhost:8000/ws');
                
                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleRealtimeUpdate(message);
                };
                
                function handleRealtimeUpdate(message) {
                    console.log('Real-time update:', message);
                    // Update UI based on message type
                    if (message.type === 'sensor_data') {
                        updateSensorDisplay(message.data);
                    } else if (message.type === 'system_event') {
                        addEventToDisplay(message.data);
                    }
                }
                
                // Load initial data
                async function loadDashboardData() {
                    try {
                        // Load system status
                        const statusResponse = await fetch('/api/status');
                        const statusData = await statusResponse.json();
                        updateSystemStatus(statusData);
                        
                        // Load zones
                        const zonesResponse = await fetch('/api/zones');
                        const zonesData = await zonesResponse.json();
                        updateZonesDisplay(zonesData.zones);
                        
                        // Load sensor data
                        const sensorsResponse = await fetch('/api/sensors');
                        const sensorsData = await sensorsResponse.json();
                        updateSensorStatus(sensorsData);
                        
                        // Load events
                        const eventsResponse = await fetch('/api/events');
                        const eventsData = await eventsResponse.json();
                        updateEventsDisplay(eventsData.events);
                        
                    } catch (error) {
                        console.error('Error loading dashboard data:', error);
                    }
                }
                
                function updateSystemStatus(data) {
                    const container = document.getElementById('system-status');
                    const isOnline = data.system.is_running;
                    
                    container.innerHTML = `
                        <div class="metric">
                            <span>Status:</span>
                            <span><span class="status-indicator ${isOnline ? 'status-online' : 'status-offline'}"></span>
                            ${isOnline ? 'Online' : 'Offline'}</span>
                        </div>
                        <div class="metric">
                            <span>Mode:</span>
                            <span>${data.system.mode}</span>
                        </div>
                        <div class="metric">
                            <span>Active Zones:</span>
                            <span>${data.system.active_irrigations}/${data.system.total_zones}</span>
                        </div>
                        <div class="metric">
                            <span>MQTT:</span>
                            <span><span class="status-indicator ${data.mqtt_connected ? 'status-online' : 'status-offline'}"></span>
                            ${data.mqtt_connected ? 'Connected' : 'Disconnected'}</span>
                        </div>
                    `;
                }
                
                function updateZonesDisplay(zones) {
                    const container = document.getElementById('zones-status');
                    
                    container.innerHTML = zones.map(zone => `
                        <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
                            <strong>${zone.name}</strong>
                            <div class="metric">
                                <span>Status:</span>
                                <span><span class="status-indicator ${getZoneStatusColor(zone.status)}"></span>${zone.status}</span>
                            </div>
                            <div class="metric">
                                <span>Moisture:</span>
                                <span>${zone.current_moisture.toFixed(1)}%</span>
                            </div>
                            <div>
                                <button class="btn btn-success" onclick="startIrrigation('${zone.zone_id}')">Start</button>
                                <button class="btn btn-danger" onclick="stopIrrigation('${zone.zone_id}')">Stop</button>
                            </div>
                        </div>
                    `).join('');
                }
                
                function getZoneStatusColor(status) {
                    switch(status) {
                        case 'irrigating': return 'status-online';
                        case 'idle': return 'status-warning';
                        case 'error': return 'status-offline';
                        default: return 'status-warning';
                    }
                }
                
                function updateSensorStatus(data) {
                    const container = document.getElementById('sensor-status');
                    const stats = data.network_stats;
                    
                    container.innerHTML = `
                        <div class="metric">
                            <span>Active Sensors:</span>
                            <span>${stats.active_sensors}/${stats.total_sensors}</span>
                        </div>
                        <div class="metric">
                            <span>Success Rate:</span>
                            <span>${stats.success_rate.toFixed(1)}%</span>
                        </div>
                        <div class="metric">
                            <span>Total Readings:</span>
                            <span>${stats.total_readings}</span>
                        </div>
                        <div class="metric">
                            <span>Uptime:</span>
                            <span>${stats.uptime_hours.toFixed(1)}h</span>
                        </div>
                    `;
                }
                
                function updateEventsDisplay(events) {
                    const container = document.getElementById('recent-events');
                    const recentEvents = events.slice(-5).reverse();
                    
                    container.innerHTML = recentEvents.map(event => `
                        <div style="padding: 5px 0; border-bottom: 1px solid #eee;">
                            <small style="color: #7f8c8d;">${new Date(event.timestamp).toLocaleTimeString()}</small><br>
                            ${event.message}
                        </div>
                    `).join('') || '<p>No recent events</p>';
                }
                
                async function startIrrigation(zoneId) {
                    try {
                        const response = await fetch(`/api/zones/${zoneId}/irrigate`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ zone_id: zoneId, action: 'start', duration_minutes: 30 })
                        });
                        
                        if (response.ok) {
                            setTimeout(loadDashboardData, 1000); // Refresh after 1 second
                        }
                    } catch (error) {
                        console.error('Error starting irrigation:', error);
                    }
                }
                
                async function stopIrrigation(zoneId) {
                    try {
                        const response = await fetch(`/api/zones/${zoneId}/irrigate`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ zone_id: zoneId, action: 'stop' })
                        });
                        
                        if (response.ok) {
                            setTimeout(loadDashboardData, 1000); // Refresh after 1 second
                        }
                    } catch (error) {
                        console.error('Error stopping irrigation:', error);
                    }
                }
                
                // Load data on page load
                loadDashboardData();
                
                // Refresh data every 30 seconds
                setInterval(loadDashboardData, 30000);
            </script>
        </body>
        </html>
        """


def create_dashboard_app(sensor_network=None, irrigation_controller=None, mqtt_client=None):
    """Create and configure dashboard application"""
    dashboard = DashboardAPI()
    
    if sensor_network or irrigation_controller or mqtt_client:
        dashboard.set_components(sensor_network, irrigation_controller, mqtt_client)
    
    return dashboard.app


if __name__ == "__main__":
    # Run standalone dashboard server
    app = create_dashboard_app()
    uvicorn.run(app, host="0.0.0.0", port=8000)
