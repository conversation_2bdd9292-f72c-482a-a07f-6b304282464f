"""
IoT Smart Irrigation System - Main Application
Complete system integration with web dashboard
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Optional
import uvicorn
from contextlib import asynccontextmanager

# Import our IoT components
from sensors.sensor_simulator import SensorNetwork
from communication.mqtt_client import MQTT<PERSON>lient
from communication.message_router import MessageRouter, MessageType
from cloud.data_processor import StreamProcessor, ProcessingRule
from control.irrigation_controller import IrrigationController, IrrigationZone, IrrigationMode
from dashboard.api_server import create_dashboard_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('irrigation_system.log')
    ]
)
logger = logging.getLogger(__name__)


class IoTIrrigationSystem:
    """
    Complete IoT Smart Irrigation System
    Integrates all components with web dashboard
    """
    
    def __init__(self):
        """Initialize the complete IoT system"""
        
        # System configuration
        self.config = {
            "farm": {
                "name": "Smart Farm Demo",
                "location": {"lat": 40.7128, "lng": -74.0060, "altitude": 100},
                "zones": [
                    {
                        "name": "zone_north",
                        "location": {"lat": 40.7130, "lng": -74.0058},
                        "soil_type": "loam",
                        "crop_type": "tomato",
                        "area_sqm": 1000
                    },
                    {
                        "name": "zone_south", 
                        "location": {"lat": 40.7126, "lng": -74.0062},
                        "soil_type": "clay",
                        "crop_type": "lettuce",
                        "area_sqm": 800
                    },
                    {
                        "name": "zone_east",
                        "location": {"lat": 40.7128, "lng": -74.0055},
                        "soil_type": "sand",
                        "crop_type": "corn",
                        "area_sqm": 1200
                    }
                ]
            },
            "mqtt": {
                "broker_host": "localhost",
                "broker_port": 1883,
                "client_id": "irrigation_main_system"
            },
            "dashboard": {
                "host": "0.0.0.0",
                "port": 8000
            },
            "sensors": {
                "reading_interval": 30,  # seconds
                "enable_simulation": True
            },
            "irrigation": {
                "mode": "automatic",
                "decision_interval": 300,  # seconds
                "enable_ml": True
            }
        }
        
        # System components
        self.sensor_network: Optional[SensorNetwork] = None
        self.mqtt_client: Optional[MQTTClient] = None
        self.message_router: Optional[MessageRouter] = None
        self.data_processor: Optional[StreamProcessor] = None
        self.irrigation_controller: Optional[IrrigationController] = None
        self.dashboard_app = None
        
        # System state
        self.is_running = False
        self.startup_time = None
        self.shutdown_event = asyncio.Event()
        
        # Setup signal handlers
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """Setup graceful shutdown signal handlers"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self):
        """Initialize all system components"""
        logger.info("🚀 Initializing IoT Smart Irrigation System...")
        
        try:
            # 1. Initialize Sensor Network
            await self._initialize_sensors()
            
            # 2. Initialize Communication
            await self._initialize_communication()
            
            # 3. Initialize Data Processing
            await self._initialize_data_processing()
            
            # 4. Initialize Irrigation Control
            await self._initialize_irrigation_control()
            
            # 5. Initialize Dashboard
            await self._initialize_dashboard()
            
            logger.info("✅ System initialization completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            return False
    
    async def _initialize_sensors(self):
        """Initialize sensor network"""
        logger.info("📡 Initializing sensor network...")
        
        self.sensor_network = SensorNetwork(self.config["farm"])
        self.sensor_network.set_reading_interval(self.config["sensors"]["reading_interval"])
        
        # Set up data callback for processing pipeline
        self.sensor_network.set_data_callback(self._process_sensor_data)
        
        logger.info(f"   ✓ Sensor network ready with {len(self.sensor_network.sensors)} sensors")
    
    async def _initialize_communication(self):
        """Initialize MQTT communication"""
        logger.info("🌐 Initializing MQTT communication...")
        
        mqtt_config = self.config["mqtt"]
        self.mqtt_client = MQTTClient(
            broker_host=mqtt_config["broker_host"],
            broker_port=mqtt_config["broker_port"],
            client_id=mqtt_config["client_id"]
        )
        
        # Try to connect (optional for demo)
        if self.mqtt_client.connect():
            logger.info("   ✓ MQTT broker connected")
        else:
            logger.warning("   ⚠️  MQTT broker not available - running in offline mode")
        
        # Initialize message router
        self.message_router = MessageRouter(
            farm_id="demo_farm",
            field_id="field_01"
        )
        
        # Add message handlers
        self.message_router.add_handler(MessageType.CONTROL_COMMAND, self._handle_control_command)
        
        logger.info("   ✓ Message routing configured")
    
    async def _initialize_data_processing(self):
        """Initialize data processing pipeline"""
        logger.info("⚙️  Initializing data processing...")
        
        self.data_processor = StreamProcessor(window_size=100)
        
        # Add processing rules
        rules = [
            ProcessingRule(
                rule_id="low_moisture_alert",
                sensor_types=["SoilMoistureSensor"],
                condition="low_moisture",
                action="send_alert",
                parameters={"threshold": 25.0, "severity": "medium"}
            ),
            ProcessingRule(
                rule_id="high_temperature_alert",
                sensor_types=["TemperatureSensor"],
                condition="high_temperature",
                action="send_alert",
                parameters={"threshold": 35.0, "severity": "high"}
            )
        ]
        
        for rule in rules:
            self.data_processor.add_processing_rule(rule)
        
        logger.info(f"   ✓ Data processing pipeline ready with {len(rules)} rules")
    
    async def _initialize_irrigation_control(self):
        """Initialize irrigation control system"""
        logger.info("🚿 Initializing irrigation control...")
        
        # Create irrigation zones
        zones = []
        for i, zone_config in enumerate(self.config["farm"]["zones"]):
            zone = IrrigationZone(
                zone_id=zone_config["name"],
                name=zone_config["name"].replace("_", " ").title(),
                area_sqm=zone_config["area_sqm"],
                crop_type=zone_config["crop_type"],
                soil_type=zone_config["soil_type"],
                sensors=[f"{zone_config['name']}_moisture_10cm"],
                valve_id=f"valve_{i+1}",
                pump_id=f"pump_{i+1}",
                target_moisture_min=35.0,
                target_moisture_max=65.0
            )
            zones.append(zone)
        
        self.irrigation_controller = IrrigationController(zones)
        
        # Set control callbacks
        self.irrigation_controller.set_valve_control_callback(self._control_valve)
        self.irrigation_controller.set_pump_control_callback(self._control_pump)
        self.irrigation_controller.set_notification_callback(self._send_notification)
        
        # Set initial mode
        mode = IrrigationMode(self.config["irrigation"]["mode"])
        self.irrigation_controller.set_mode(mode)
        
        logger.info(f"   ✓ Irrigation controller ready with {len(zones)} zones in {mode.value} mode")
    
    async def _initialize_dashboard(self):
        """Initialize web dashboard"""
        logger.info("🖥️  Initializing web dashboard...")
        
        self.dashboard_app = create_dashboard_app(
            sensor_network=self.sensor_network,
            irrigation_controller=self.irrigation_controller,
            mqtt_client=self.mqtt_client
        )
        
        logger.info("   ✓ Web dashboard ready")
    
    async def start(self):
        """Start the complete IoT system"""
        if self.is_running:
            logger.warning("System is already running")
            return
        
        logger.info("🎬 Starting IoT Smart Irrigation System...")
        self.is_running = True
        self.startup_time = datetime.now()
        
        try:
            # Start all background tasks
            tasks = []
            
            # Start sensor monitoring
            if self.sensor_network:
                tasks.append(asyncio.create_task(self.sensor_network.start_monitoring()))
            
            # Start irrigation control
            if self.irrigation_controller:
                tasks.append(asyncio.create_task(self.irrigation_controller.start_control_loop()))
            
            # Start web dashboard
            dashboard_config = self.config["dashboard"]
            tasks.append(asyncio.create_task(
                self._run_dashboard_server(dashboard_config["host"], dashboard_config["port"])
            ))
            
            # Wait for shutdown signal
            tasks.append(asyncio.create_task(self.shutdown_event.wait()))
            
            logger.info("✅ All systems started successfully!")
            logger.info(f"🌐 Dashboard available at: http://localhost:{dashboard_config['port']}")
            
            # Wait for any task to complete (usually shutdown)
            done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
            
            # Cancel remaining tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
        except Exception as e:
            logger.error(f"Error running system: {e}")
        finally:
            self.is_running = False
    
    async def _run_dashboard_server(self, host: str, port: int):
        """Run the dashboard web server"""
        config = uvicorn.Config(
            app=self.dashboard_app,
            host=host,
            port=port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve()
    
    async def _process_sensor_data(self, network_data):
        """Process sensor data through the pipeline"""
        # This integrates sensor data with the processing pipeline
        # and updates irrigation controller
        
        for sensor_id, sensor_data in network_data.get("sensors", {}).items():
            if "error" in sensor_data:
                continue
            
            # Update irrigation controller with soil moisture data
            if "moisture" in sensor_id:
                zone_id = self._get_zone_from_sensor(sensor_id)
                if zone_id in self.irrigation_controller.zones:
                    zone = self.irrigation_controller.zones[zone_id]
                    zone.current_moisture = sensor_data.get("value", zone.current_moisture)
    
    def _get_zone_from_sensor(self, sensor_id: str) -> str:
        """Extract zone ID from sensor ID"""
        for zone_config in self.config["farm"]["zones"]:
            if sensor_id.startswith(zone_config["name"]):
                return zone_config["name"]
        return "unknown"
    
    async def _handle_control_command(self, message_type, topic: str, payload: dict):
        """Handle irrigation control commands"""
        command = payload.get("command")
        target = payload.get("target")
        
        if command == "start_irrigation" and target:
            duration = payload.get("parameters", {}).get("duration", 30)
            await self.irrigation_controller.manual_start_irrigation(target, duration)
        elif command == "stop_irrigation" and target:
            await self.irrigation_controller.manual_stop_irrigation(target)
    
    async def _control_valve(self, valve_id: str, state: bool):
        """Hardware valve control simulation"""
        action = "OPEN" if state else "CLOSE"
        logger.info(f"🚿 Hardware Control - Valve {valve_id}: {action}")
    
    async def _control_pump(self, pump_id: str, state: bool):
        """Hardware pump control simulation"""
        action = "START" if state else "STOP"
        logger.info(f"⚡ Hardware Control - Pump {pump_id}: {action}")
    
    async def _send_notification(self, notification: dict):
        """Send system notification"""
        logger.info(f"📢 System Notification: {notification}")
    
    async def shutdown(self):
        """Graceful system shutdown"""
        if not self.is_running:
            return
        
        logger.info("🛑 Initiating system shutdown...")
        
        # Stop all components
        if self.sensor_network:
            self.sensor_network.stop_monitoring()
        
        if self.irrigation_controller:
            self.irrigation_controller.stop_control_loop()
        
        if self.mqtt_client:
            self.mqtt_client.disconnect()
        
        # Signal shutdown
        self.shutdown_event.set()
        
        uptime = datetime.now() - self.startup_time if self.startup_time else None
        logger.info(f"✅ System shutdown completed. Uptime: {uptime}")
    
    def get_system_info(self):
        """Get comprehensive system information"""
        return {
            "system": {
                "name": "IoT Smart Irrigation System",
                "version": "1.0.0",
                "startup_time": self.startup_time.isoformat() if self.startup_time else None,
                "is_running": self.is_running
            },
            "configuration": self.config,
            "components": {
                "sensor_network": self.sensor_network is not None,
                "mqtt_client": self.mqtt_client is not None,
                "irrigation_controller": self.irrigation_controller is not None,
                "dashboard": self.dashboard_app is not None
            }
        }


async def main():
    """Main application entry point"""
    print("🌱 IoT Smart Irrigation System")
    print("=" * 50)
    print("Complete IoT platform demonstrating:")
    print("• Realistic sensor simulation and data collection")
    print("• MQTT communication with proper IoT protocols")
    print("• Real-time data processing and analytics")
    print("• Intelligent irrigation control with ML")
    print("• Web dashboard for monitoring and control")
    print("• Complete system integration and automation")
    print("=" * 50)
    print()
    
    # Create and initialize system
    system = IoTIrrigationSystem()
    
    if await system.initialize():
        logger.info("System initialized successfully, starting operations...")
        await system.start()
    else:
        logger.error("System initialization failed")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)
