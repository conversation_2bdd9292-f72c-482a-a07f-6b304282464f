"""
Soil Moisture Sensor Implementation
Simulates capacitive soil moisture sensors commonly used in IoT irrigation systems
"""

import math
import random
from datetime import datetime, timedelta
from .base_sensor import BaseSensor


class SoilMoistureSensor(BaseSensor):
    """
    Soil moisture sensor simulation with realistic behavior patterns.
    Simulates capacitive sensors that measure soil dielectric properties.
    """
    
    def __init__(self, 
                 sensor_id: str, 
                 location: dict,
                 soil_type: str = "loam",
                 depth: float = 15.0,  # cm
                 calibration_offset: float = 0.0,
                 noise_level: float = 0.02):
        """
        Initialize soil moisture sensor
        
        Args:
            sensor_id: Unique sensor identifier
            location: GPS coordinates
            soil_type: Type of soil (sand, clay, loam, silt)
            depth: Sensor depth in centimeters
            calibration_offset: Calibration offset
            noise_level: Sensor noise level
        """
        super().__init__(sensor_id, location, calibration_offset, noise_level)
        
        self.soil_type = soil_type
        self.depth = depth
        
        # Soil-specific properties affecting moisture readings
        self.soil_properties = {
            "sand": {"porosity": 0.43, "field_capacity": 0.09, "wilting_point": 0.04},
            "clay": {"porosity": 0.51, "field_capacity": 0.31, "wilting_point": 0.15},
            "loam": {"porosity": 0.47, "field_capacity": 0.22, "wilting_point": 0.10},
            "silt": {"porosity": 0.49, "field_capacity": 0.28, "wilting_point": 0.12}
        }
        
        # Current soil state
        self.current_moisture = self.soil_properties[soil_type]["field_capacity"] * 0.7
        self.last_irrigation = None
        self.last_rain = None
        
        # Environmental factors
        self.temperature_effect = 0.0
        self.evaporation_rate = 0.001  # per hour
        
    def get_unit(self) -> str:
        """Return moisture percentage unit"""
        return "%"
    
    def simulate_evaporation(self):
        """
        Simulate water evaporation from soil
        Realistic evaporation based on temperature and time
        """
        if self.last_reading_time:
            hours_passed = (datetime.now() - self.last_reading_time).total_seconds() / 3600
            
            # Temperature-dependent evaporation (higher temp = more evaporation)
            temp_factor = 1.0 + (self.temperature_effect / 100.0)
            evaporation = self.evaporation_rate * hours_passed * temp_factor
            
            # Depth factor (deeper sensors lose moisture slower)
            depth_factor = max(0.5, 1.0 - (self.depth / 100.0))
            evaporation *= depth_factor
            
            # Reduce moisture due to evaporation
            self.current_moisture = max(
                self.soil_properties[self.soil_type]["wilting_point"],
                self.current_moisture - evaporation
            )
    
    def simulate_irrigation_effect(self):
        """
        Simulate effect of recent irrigation
        Moisture increases gradually after irrigation
        """
        if self.last_irrigation:
            hours_since_irrigation = (datetime.now() - self.last_irrigation).total_seconds() / 3600
            
            # Irrigation effect diminishes over time
            if hours_since_irrigation < 24:
                irrigation_boost = 0.1 * math.exp(-hours_since_irrigation / 8)
                field_capacity = self.soil_properties[self.soil_type]["field_capacity"]
                target_moisture = min(field_capacity, self.current_moisture + irrigation_boost)
                
                # Gradual moisture increase
                self.current_moisture = min(target_moisture, self.current_moisture + 0.01)
    
    def simulate_rain_effect(self):
        """
        Simulate effect of rainfall
        Random rain events that increase soil moisture
        """
        # Random rain simulation (1% chance per reading)
        if random.random() < 0.01:
            rain_amount = random.uniform(0.05, 0.25)  # 5-25% moisture increase
            field_capacity = self.soil_properties[self.soil_type]["field_capacity"]
            
            self.current_moisture = min(
                field_capacity * 1.1,  # Can exceed field capacity temporarily
                self.current_moisture + rain_amount
            )
            self.last_rain = datetime.now()
    
    def apply_temperature_effect(self, temperature: float):
        """
        Apply temperature effect on moisture readings
        Higher temperatures increase evaporation
        """
        self.temperature_effect = temperature
    
    def simulate_irrigation(self, duration_minutes: float = 30):
        """
        Simulate irrigation event
        Called when irrigation system activates
        """
        self.last_irrigation = datetime.now()
        
        # Immediate moisture increase based on irrigation duration
        irrigation_amount = min(0.15, duration_minutes / 200.0)  # Max 15% increase
        field_capacity = self.soil_properties[self.soil_type]["field_capacity"]
        
        self.current_moisture = min(
            field_capacity,
            self.current_moisture + irrigation_amount
        )
    
    def read_raw_value(self) -> float:
        """
        Read raw soil moisture value with realistic behavior
        Returns moisture percentage (0-100)
        """
        # Simulate environmental effects
        self.simulate_evaporation()
        self.simulate_irrigation_effect()
        self.simulate_rain_effect()
        
        # Convert to percentage (0-100)
        moisture_percentage = self.current_moisture * 100
        
        # Add soil-type specific variations
        if self.soil_type == "clay":
            # Clay holds water longer but readings can be less accurate
            moisture_percentage += random.uniform(-2, 2)
        elif self.soil_type == "sand":
            # Sandy soil drains quickly, more variable readings
            moisture_percentage += random.uniform(-3, 3)
        
        # Ensure realistic bounds
        return max(0, min(100, moisture_percentage))
    
    def get_soil_condition(self) -> str:
        """
        Get human-readable soil condition
        Useful for dashboard displays
        """
        moisture_ratio = self.current_moisture / self.soil_properties[self.soil_type]["field_capacity"]
        
        if moisture_ratio < 0.3:
            return "Very Dry"
        elif moisture_ratio < 0.5:
            return "Dry"
        elif moisture_ratio < 0.8:
            return "Optimal"
        elif moisture_ratio < 1.0:
            return "Moist"
        else:
            return "Saturated"
    
    def needs_irrigation(self) -> bool:
        """
        Determine if irrigation is needed
        Based on soil moisture thresholds
        """
        wilting_point = self.soil_properties[self.soil_type]["wilting_point"]
        field_capacity = self.soil_properties[self.soil_type]["field_capacity"]
        
        # Irrigation needed when moisture drops below 40% of field capacity
        threshold = wilting_point + (field_capacity - wilting_point) * 0.4
        
        return self.current_moisture < threshold
    
    def read(self) -> dict:
        """
        Enhanced read method with soil-specific data
        """
        base_data = super().read()
        
        if "error" not in base_data:
            # Add soil-specific information
            base_data.update({
                "soil_type": self.soil_type,
                "depth_cm": self.depth,
                "soil_condition": self.get_soil_condition(),
                "needs_irrigation": self.needs_irrigation(),
                "field_capacity": round(self.soil_properties[self.soil_type]["field_capacity"] * 100, 1),
                "wilting_point": round(self.soil_properties[self.soil_type]["wilting_point"] * 100, 1),
                "last_irrigation": self.last_irrigation.isoformat() if self.last_irrigation else None,
                "last_rain": self.last_rain.isoformat() if self.last_rain else None
            })
        
        return base_data
