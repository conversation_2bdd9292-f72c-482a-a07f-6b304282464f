# IoT Smart Irrigation System - API Documentation

## 🌐 API Overview

The IoT Smart Irrigation System provides a comprehensive REST API for monitoring and controlling irrigation systems. The API follows RESTful principles and returns JSON responses.

**Base URL**: `http://localhost:8000/api`
**Documentation**: `http://localhost:8000/docs` (Interactive Swagger UI)

## 🔐 Authentication

Most endpoints require authentication using JWT tokens.

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### Using Authentication
Include the token in the Authorization header:
```http
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 📊 System Status

### Get System Status
```http
GET /api/status
```

**Response:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "system": {
    "is_running": true,
    "mode": "automatic",
    "total_zones": 3,
    "active_irrigations": 1,
    "ml_model_trained": true
  },
  "network": {
    "total_sensors": 12,
    "active_sensors": 11,
    "success_rate": 98.5,
    "uptime_hours": 24.5
  },
  "mqtt_connected": true
}
```

## 🚿 Irrigation Zones

### Get All Zones
```http
GET /api/zones
```

**Response:**
```json
{
  "zones": [
    {
      "zone_id": "zone_north",
      "name": "Zone North",
      "status": "idle",
      "current_moisture": 45.2,
      "target_moisture_min": 35.0,
      "target_moisture_max": 65.0,
      "last_irrigation": "2024-01-15T08:30:00Z",
      "total_water_used": 245.5,
      "needs_irrigation": false,
      "can_irrigate": true
    }
  ]
}
```

### Get Zone Details
```http
GET /api/zones/{zone_id}
```

**Response:**
```json
{
  "zone_id": "zone_north",
  "name": "Zone North",
  "status": "idle",
  "current_moisture": 45.2,
  "sensors": {
    "zone_north_moisture_10cm": {
      "value": 45.2,
      "unit": "%",
      "timestamp": "2024-01-15T10:29:00Z",
      "quality_score": 0.95
    },
    "zone_north_environment": {
      "temperature": 26.5,
      "humidity": 65.0,
      "pressure": 1013.2
    }
  }
}
```

### Control Irrigation
```http
POST /api/zones/{zone_id}/irrigate
Content-Type: application/json

{
  "action": "start",
  "duration_minutes": 30
}
```

**Response:**
```json
{
  "success": true,
  "message": "Irrigation start command executed",
  "zone_id": "zone_north",
  "action": "start",
  "duration_minutes": 30
}
```

## 📡 Sensors

### Get Sensor Data
```http
GET /api/sensors
```

**Response:**
```json
{
  "sensors": {
    "zone_north_moisture_10cm": {
      "sensor_id": "zone_north_moisture_10cm",
      "is_active": true,
      "battery_level": 87.5,
      "signal_strength": -65,
      "health_status": "healthy",
      "total_readings": 1440
    }
  },
  "network_stats": {
    "total_sensors": 12,
    "active_sensors": 11,
    "success_rate": 98.5
  }
}
```

### Get Sensor History
```http
GET /api/sensors/{sensor_id}/history?hours=24
```

**Response:**
```json
{
  "sensor_id": "zone_north_moisture_10cm",
  "start_time": "2024-01-14T10:30:00Z",
  "end_time": "2024-01-15T10:30:00Z",
  "data_points": 48,
  "history": [
    {
      "timestamp": "2024-01-15T10:30:00Z",
      "value": 45.2,
      "quality_score": 0.95
    }
  ]
}
```

## 🚨 Alerts

### Get Active Alerts
```http
GET /api/alerts
```

**Response:**
```json
{
  "alerts": [
    {
      "id": "alert_001",
      "type": "low_moisture",
      "severity": "medium",
      "title": "Low Soil Moisture Detected",
      "message": "Zone North moisture level is below threshold (25%)",
      "timestamp": "2024-01-15T10:25:00Z",
      "acknowledged": false,
      "source": "zone_north_moisture_10cm"
    }
  ]
}
```

### Acknowledge Alert
```http
POST /api/alerts/{alert_id}/acknowledge
```

**Response:**
```json
{
  "success": true,
  "alert_id": "alert_001",
  "acknowledged_at": "2024-01-15T10:35:00Z"
}
```

## 🎯 Irrigation Decisions

### Get Recent Decisions
```http
GET /api/decisions?limit=10
```

**Response:**
```json
{
  "decisions": [
    {
      "zone_id": "zone_north",
      "action": "start",
      "duration_minutes": 30,
      "reason": "moisture_low_start_irrigation",
      "confidence": 0.85,
      "timestamp": "2024-01-15T10:30:00Z",
      "sensor_data": {
        "soil_moisture": 25.5,
        "temperature": 28.0
      }
    }
  ]
}
```

## ⚙️ System Configuration

### Get System Mode
```http
GET /api/system/mode
```

**Response:**
```json
{
  "mode": "automatic",
  "available_modes": ["manual", "automatic", "scheduled"]
}
```

### Set System Mode
```http
POST /api/system/mode
Content-Type: application/json

{
  "mode": "manual",
  "reading_interval": 60
}
```

**Response:**
```json
{
  "success": true,
  "mode": "manual",
  "previous_mode": "automatic"
}
```

## 📈 Analytics

### Get System Analytics
```http
GET /api/analytics/summary?days=7
```

**Response:**
```json
{
  "period_days": 7,
  "water_usage": {
    "total_liters": 1250.5,
    "daily_average": 178.6,
    "efficiency_score": 82.5
  },
  "irrigation_events": 15,
  "sensor_readings": 10080,
  "system_uptime": 99.2
}
```

### Generate Report
```http
POST /api/analytics/report
Content-Type: application/json

{
  "report_type": "comprehensive",
  "period_days": 30,
  "include_charts": true
}
```

**Response:**
```json
{
  "report_id": "report_20240115_103000",
  "status": "generated",
  "download_url": "/api/reports/report_20240115_103000.pdf",
  "expires_at": "2024-01-22T10:30:00Z"
}
```

## 👥 User Management

### Get Users
```http
GET /api/users
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "users": [
    {
      "user_id": "user_001",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin",
      "active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "last_login": "2024-01-15T09:00:00Z"
    }
  ]
}
```

### Create User
```http
POST /api/users
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "username": "operator1",
  "email": "<EMAIL>",
  "password": "secure_password",
  "role": "operator"
}
```

## 🔧 Device Management

### Register Device
```http
POST /api/devices
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "device_id": "sensor_new_001",
  "name": "New Moisture Sensor",
  "device_type": "sensor",
  "location": {"lat": 40.7128, "lng": -74.0060},
  "zone_id": "zone_north",
  "model": "SoilSense Pro",
  "ip_address": "*************"
}
```

### Update Device
```http
PUT /api/devices/{device_id}
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "name": "Updated Sensor Name",
  "active": true,
  "configuration": {
    "reading_interval": 30,
    "calibration_offset": 0.5
  }
}
```

## 📱 WebSocket API

### Real-time Updates
Connect to WebSocket for real-time data:

```javascript
const ws = new WebSocket('ws://localhost:8000/ws');

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    
    switch(message.type) {
        case 'sensor_data':
            // Handle sensor data update
            break;
        case 'system_event':
            // Handle system event
            break;
        case 'alert':
            // Handle new alert
            break;
    }
};
```

**Message Types:**
- `sensor_data`: Real-time sensor readings
- `system_event`: System status changes
- `alert`: New alerts and notifications
- `irrigation_status`: Irrigation start/stop events

## 📋 Error Handling

### Standard Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid zone_id provided",
    "details": {
      "field": "zone_id",
      "value": "invalid_zone"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error
- `503`: Service Unavailable

## 🔄 Rate Limiting

API endpoints are rate-limited to prevent abuse:
- **General endpoints**: 100 requests per minute
- **Authentication**: 10 requests per minute
- **Control commands**: 20 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## 📚 SDK and Examples

### Python SDK Example
```python
import requests

class IrrigationAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def get_zones(self):
        response = requests.get(f'{self.base_url}/zones', headers=self.headers)
        return response.json()
    
    def start_irrigation(self, zone_id, duration=30):
        data = {'action': 'start', 'duration_minutes': duration}
        response = requests.post(
            f'{self.base_url}/zones/{zone_id}/irrigate',
            json=data, headers=self.headers
        )
        return response.json()

# Usage
api = IrrigationAPI('http://localhost:8000/api', 'your_token')
zones = api.get_zones()
result = api.start_irrigation('zone_north', 45)
```

### JavaScript SDK Example
```javascript
class IrrigationAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getZones() {
        const response = await fetch(`${this.baseUrl}/zones`, {
            headers: this.headers
        });
        return response.json();
    }
    
    async startIrrigation(zoneId, duration = 30) {
        const response = await fetch(`${this.baseUrl}/zones/${zoneId}/irrigate`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify({
                action: 'start',
                duration_minutes: duration
            })
        });
        return response.json();
    }
}

// Usage
const api = new IrrigationAPI('http://localhost:8000/api', 'your_token');
const zones = await api.getZones();
const result = await api.startIrrigation('zone_north', 45);
```

## 🧪 Testing

### API Testing with curl
```bash
# Get system status
curl -X GET http://localhost:8000/api/status

# Login and get token
TOKEN=$(curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  | jq -r '.access_token')

# Get zones with authentication
curl -X GET http://localhost:8000/api/zones \
  -H "Authorization: Bearer $TOKEN"

# Start irrigation
curl -X POST http://localhost:8000/api/zones/zone_north/irrigate \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"action":"start","duration_minutes":30}'
```

For more detailed examples and interactive testing, visit the Swagger UI at `http://localhost:8000/docs`.
