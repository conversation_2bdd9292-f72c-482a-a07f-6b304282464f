"""
MQTT Message Router for IoT Smart Irrigation System
Handles message routing, topic management, and protocol standardization
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Callable, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Standard message types for IoT irrigation system"""
    SENSOR_DATA = "sensor_data"
    CONTROL_COMMAND = "control_command"
    STATUS_UPDATE = "status_update"
    ALERT = "alert"
    CONFIGURATION = "configuration"
    HEARTBEAT = "heartbeat"
    SYSTEM_EVENT = "system_event"


class QoSLevel(Enum):
    """MQTT Quality of Service levels"""
    AT_MOST_ONCE = 0    # Fire and forget
    AT_LEAST_ONCE = 1   # Acknowledged delivery
    EXACTLY_ONCE = 2    # Assured delivery


@dataclass
class TopicStructure:
    """Standard topic structure for IoT irrigation system"""
    base: str = "irrigation"
    farm_id: str = "default_farm"
    field_id: str = "field_01"
    zone_id: Optional[str] = None
    device_type: Optional[str] = None
    device_id: Optional[str] = None
    message_type: Optional[str] = None
    
    def build_topic(self) -> str:
        """Build MQTT topic from structure"""
        parts = [self.base, self.farm_id, self.field_id]
        
        if self.zone_id:
            parts.append(self.zone_id)
        if self.device_type:
            parts.append(self.device_type)
        if self.device_id:
            parts.append(self.device_id)
        if self.message_type:
            parts.append(self.message_type)
        
        return "/".join(parts)
    
    @classmethod
    def from_topic(cls, topic: str) -> 'TopicStructure':
        """Parse topic string into structure"""
        parts = topic.split("/")
        
        structure = cls()
        if len(parts) >= 1:
            structure.base = parts[0]
        if len(parts) >= 2:
            structure.farm_id = parts[1]
        if len(parts) >= 3:
            structure.field_id = parts[2]
        if len(parts) >= 4:
            structure.zone_id = parts[3]
        if len(parts) >= 5:
            structure.device_type = parts[4]
        if len(parts) >= 6:
            structure.device_id = parts[5]
        if len(parts) >= 7:
            structure.message_type = parts[6]
        
        return structure


class MessageRouter:
    """
    Routes MQTT messages based on topic structure and message type
    Implements standardized IoT messaging patterns
    """
    
    def __init__(self, farm_id: str = "default_farm", field_id: str = "field_01"):
        """
        Initialize message router
        
        Args:
            farm_id: Unique farm identifier
            field_id: Unique field identifier
        """
        self.farm_id = farm_id
        self.field_id = field_id
        
        # Message handlers by type
        self.handlers: Dict[MessageType, List[Callable]] = {
            msg_type: [] for msg_type in MessageType
        }
        
        # Topic-specific handlers
        self.topic_handlers: Dict[str, List[Callable]] = {}
        
        # Message validation rules
        self.validation_rules: Dict[MessageType, Callable] = {}
        
        # Message statistics
        self.message_stats = {
            "total_processed": 0,
            "by_type": {msg_type.value: 0 for msg_type in MessageType},
            "validation_errors": 0,
            "routing_errors": 0
        }
    
    def add_handler(self, message_type: MessageType, handler: Callable):
        """Add message handler for specific message type"""
        self.handlers[message_type].append(handler)
        logger.info(f"Added handler for message type: {message_type.value}")
    
    def add_topic_handler(self, topic_pattern: str, handler: Callable):
        """Add handler for specific topic pattern"""
        if topic_pattern not in self.topic_handlers:
            self.topic_handlers[topic_pattern] = []
        self.topic_handlers[topic_pattern].append(handler)
        logger.info(f"Added handler for topic pattern: {topic_pattern}")
    
    def add_validation_rule(self, message_type: MessageType, validator: Callable):
        """Add validation rule for message type"""
        self.validation_rules[message_type] = validator
        logger.info(f"Added validation rule for message type: {message_type.value}")
    
    def route_message(self, topic: str, payload: Dict[str, Any]) -> bool:
        """
        Route incoming message to appropriate handlers
        
        Args:
            topic: MQTT topic
            payload: Message payload
            
        Returns:
            True if message was successfully routed, False otherwise
        """
        try:
            self.message_stats["total_processed"] += 1
            
            # Parse topic structure
            topic_structure = TopicStructure.from_topic(topic)
            
            # Determine message type
            message_type = self._determine_message_type(topic_structure, payload)
            
            if message_type:
                self.message_stats["by_type"][message_type.value] += 1
                
                # Validate message
                if not self._validate_message(message_type, payload):
                    self.message_stats["validation_errors"] += 1
                    return False
                
                # Route to type-specific handlers
                success = self._route_to_type_handlers(message_type, topic, payload)
                
                # Route to topic-specific handlers
                success = self._route_to_topic_handlers(topic, payload) or success
                
                return success
            else:
                logger.warning(f"Could not determine message type for topic: {topic}")
                return False
                
        except Exception as e:
            logger.error(f"Error routing message from topic {topic}: {e}")
            self.message_stats["routing_errors"] += 1
            return False
    
    def _determine_message_type(self, topic_structure: TopicStructure, payload: Dict[str, Any]) -> Optional[MessageType]:
        """Determine message type from topic and payload"""
        
        # Check explicit message type in topic
        if topic_structure.message_type:
            try:
                return MessageType(topic_structure.message_type)
            except ValueError:
                pass
        
        # Check message type in payload
        if "message_type" in payload:
            try:
                return MessageType(payload["message_type"])
            except ValueError:
                pass
        
        # Infer from topic structure and content
        if topic_structure.device_type == "sensor" or "sensors" in payload:
            return MessageType.SENSOR_DATA
        elif topic_structure.device_type == "controller" or "command" in payload:
            return MessageType.CONTROL_COMMAND
        elif "status" in topic_structure.build_topic().lower():
            return MessageType.STATUS_UPDATE
        elif "alert" in payload or "alarm" in payload:
            return MessageType.ALERT
        elif "config" in topic_structure.build_topic().lower():
            return MessageType.CONFIGURATION
        elif "heartbeat" in payload:
            return MessageType.HEARTBEAT
        
        return None
    
    def _validate_message(self, message_type: MessageType, payload: Dict[str, Any]) -> bool:
        """Validate message against type-specific rules"""
        
        # Check for custom validation rule
        if message_type in self.validation_rules:
            try:
                return self.validation_rules[message_type](payload)
            except Exception as e:
                logger.error(f"Error in custom validation for {message_type.value}: {e}")
                return False
        
        # Default validation rules
        required_fields = {
            MessageType.SENSOR_DATA: ["timestamp", "sensor_id"],
            MessageType.CONTROL_COMMAND: ["timestamp", "command", "target"],
            MessageType.STATUS_UPDATE: ["timestamp", "device_id", "status"],
            MessageType.ALERT: ["timestamp", "alert_type", "message"],
            MessageType.CONFIGURATION: ["timestamp", "config_type"],
            MessageType.HEARTBEAT: ["timestamp", "device_id"],
            MessageType.SYSTEM_EVENT: ["timestamp", "event_type"]
        }
        
        required = required_fields.get(message_type, ["timestamp"])
        
        for field in required:
            if field not in payload:
                logger.warning(f"Missing required field '{field}' for message type {message_type.value}")
                return False
        
        return True
    
    def _route_to_type_handlers(self, message_type: MessageType, topic: str, payload: Dict[str, Any]) -> bool:
        """Route message to type-specific handlers"""
        handlers = self.handlers.get(message_type, [])
        
        if not handlers:
            logger.debug(f"No handlers registered for message type: {message_type.value}")
            return False
        
        success = False
        for handler in handlers:
            try:
                handler(message_type, topic, payload)
                success = True
            except Exception as e:
                logger.error(f"Error in handler for {message_type.value}: {e}")
        
        return success
    
    def _route_to_topic_handlers(self, topic: str, payload: Dict[str, Any]) -> bool:
        """Route message to topic-specific handlers"""
        success = False
        
        for pattern, handlers in self.topic_handlers.items():
            if self._topic_matches_pattern(topic, pattern):
                for handler in handlers:
                    try:
                        handler(topic, payload)
                        success = True
                    except Exception as e:
                        logger.error(f"Error in topic handler for {pattern}: {e}")
        
        return success
    
    def _topic_matches_pattern(self, topic: str, pattern: str) -> bool:
        """Check if topic matches pattern with wildcards"""
        topic_parts = topic.split('/')
        pattern_parts = pattern.split('/')
        
        if len(pattern_parts) > len(topic_parts):
            return False
        
        for i, pattern_part in enumerate(pattern_parts):
            if pattern_part == '#':
                return True
            elif pattern_part == '+':
                continue
            elif i >= len(topic_parts) or pattern_part != topic_parts[i]:
                return False
        
        return len(topic_parts) == len(pattern_parts)
    
    def create_sensor_topic(self, zone_id: str, device_id: str) -> str:
        """Create standardized sensor data topic"""
        structure = TopicStructure(
            farm_id=self.farm_id,
            field_id=self.field_id,
            zone_id=zone_id,
            device_type="sensor",
            device_id=device_id,
            message_type="data"
        )
        return structure.build_topic()
    
    def create_control_topic(self, zone_id: str, device_id: str) -> str:
        """Create standardized control command topic"""
        structure = TopicStructure(
            farm_id=self.farm_id,
            field_id=self.field_id,
            zone_id=zone_id,
            device_type="controller",
            device_id=device_id,
            message_type="command"
        )
        return structure.build_topic()
    
    def create_status_topic(self, device_id: str) -> str:
        """Create standardized status topic"""
        structure = TopicStructure(
            farm_id=self.farm_id,
            field_id=self.field_id,
            device_type="device",
            device_id=device_id,
            message_type="status"
        )
        return structure.build_topic()
    
    def create_alert_topic(self) -> str:
        """Create standardized alert topic"""
        structure = TopicStructure(
            farm_id=self.farm_id,
            field_id=self.field_id,
            message_type="alerts"
        )
        return structure.build_topic()
    
    def format_sensor_message(self, sensor_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format sensor data into standard message format"""
        return {
            "message_type": MessageType.SENSOR_DATA.value,
            "timestamp": datetime.now().isoformat(),
            "farm_id": self.farm_id,
            "field_id": self.field_id,
            "sensor_data": sensor_data,
            "qos": QoSLevel.AT_LEAST_ONCE.value
        }
    
    def format_control_message(self, command: str, target: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Format control command into standard message format"""
        return {
            "message_type": MessageType.CONTROL_COMMAND.value,
            "timestamp": datetime.now().isoformat(),
            "farm_id": self.farm_id,
            "field_id": self.field_id,
            "command": command,
            "target": target,
            "parameters": parameters or {},
            "qos": QoSLevel.AT_LEAST_ONCE.value
        }
    
    def format_alert_message(self, alert_type: str, message: str, severity: str = "medium") -> Dict[str, Any]:
        """Format alert into standard message format"""
        return {
            "message_type": MessageType.ALERT.value,
            "timestamp": datetime.now().isoformat(),
            "farm_id": self.farm_id,
            "field_id": self.field_id,
            "alert_type": alert_type,
            "message": message,
            "severity": severity,
            "qos": QoSLevel.AT_LEAST_ONCE.value
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get message routing statistics"""
        return {
            "message_stats": self.message_stats.copy(),
            "registered_handlers": {
                msg_type.value: len(handlers) 
                for msg_type, handlers in self.handlers.items()
            },
            "topic_handlers": len(self.topic_handlers),
            "validation_rules": len(self.validation_rules)
        }
