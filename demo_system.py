"""
IoT Smart Irrigation System - Complete Demo
Demonstrates the entire IoT ecosystem working together
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import time

# Import our IoT components
from sensors.sensor_simulator import SensorNetwork
from communication.mqtt_client import <PERSON><PERSON><PERSON><PERSON>lient
from communication.message_router import MessageRouter, MessageType
from cloud.data_processor import StreamProcessor, SensorReading, ProcessingRule
from cloud.data_storage import DataStorageManager, StorageConfig
from control.irrigation_controller import IrrigationController, IrrigationZone, IrrigationMode

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class IoTIrrigationDemo:
    """
    Complete IoT Smart Irrigation System Demo
    Demonstrates real-world IoT engineering principles and data flow
    """
    
    def __init__(self):
        """Initialize the complete IoT system"""
        
        # System configuration
        self.farm_config = {
            "name": "Smart Farm Demo",
            "center_location": {"lat": 40.7128, "lng": -74.0060, "altitude": 100},
            "zones": [
                {
                    "name": "zone_north",
                    "location": {"lat": 40.7130, "lng": -74.0058},
                    "soil_type": "loam",
                    "crop_type": "tomato",
                    "area_sqm": 1000
                },
                {
                    "name": "zone_south", 
                    "location": {"lat": 40.7126, "lng": -74.0062},
                    "soil_type": "clay",
                    "crop_type": "lettuce",
                    "area_sqm": 800
                },
                {
                    "name": "zone_east",
                    "location": {"lat": 40.7128, "lng": -74.0055},
                    "soil_type": "sand",
                    "crop_type": "corn",
                    "area_sqm": 1200
                }
            ]
        }
        
        # Initialize components
        self.sensor_network = None
        self.mqtt_client = None
        self.message_router = None
        self.data_processor = None
        self.data_storage = None
        self.irrigation_controller = None
        
        # Demo state
        self.is_running = False
        self.demo_start_time = None
        self.message_count = 0
        
    async def initialize_system(self):
        """Initialize all IoT system components"""
        logger.info("🚀 Initializing IoT Smart Irrigation System...")
        
        # 1. Initialize Sensor Network
        logger.info("📡 Setting up sensor network...")
        self.sensor_network = SensorNetwork(self.farm_config)
        self.sensor_network.set_data_callback(self._handle_sensor_data)
        self.sensor_network.set_reading_interval(30)  # 30 seconds for demo
        
        # 2. Initialize MQTT Communication
        logger.info("🌐 Setting up MQTT communication...")
        self.mqtt_client = MQTTClient(
            broker_host="localhost",
            broker_port=1883,
            client_id="irrigation_demo_client"
        )
        
        # Connect to MQTT broker (will use local broker for demo)
        if not self.mqtt_client.connect():
            logger.warning("⚠️  MQTT broker not available - running in offline mode")
        
        # 3. Initialize Message Router
        logger.info("🔀 Setting up message routing...")
        self.message_router = MessageRouter(
            farm_id="demo_farm",
            field_id="field_01"
        )
        
        # Add message handlers
        self.message_router.add_handler(MessageType.SENSOR_DATA, self._handle_sensor_message)
        self.message_router.add_handler(MessageType.CONTROL_COMMAND, self._handle_control_message)
        
        # 4. Initialize Data Processing
        logger.info("⚙️  Setting up data processing...")
        self.data_processor = StreamProcessor(window_size=50)
        
        # Add processing rules
        self._setup_processing_rules()
        
        # 5. Initialize Data Storage (mock for demo)
        logger.info("💾 Setting up data storage...")
        storage_config = StorageConfig()
        self.data_storage = DataStorageManager(storage_config)
        
        # Note: In a real deployment, you would initialize actual databases
        logger.info("ℹ️  Using mock storage for demo (no actual databases required)")
        
        # 6. Initialize Irrigation Controller
        logger.info("🚿 Setting up irrigation controller...")
        irrigation_zones = self._create_irrigation_zones()
        self.irrigation_controller = IrrigationController(irrigation_zones)
        
        # Set up hardware control callbacks (simulated)
        self.irrigation_controller.set_valve_control_callback(self._control_valve)
        self.irrigation_controller.set_pump_control_callback(self._control_pump)
        self.irrigation_controller.set_notification_callback(self._send_notification)
        
        logger.info("✅ IoT Smart Irrigation System initialized successfully!")
        
    def _create_irrigation_zones(self):
        """Create irrigation zones from farm configuration"""
        zones = []
        
        for i, zone_config in enumerate(self.farm_config["zones"]):
            zone = IrrigationZone(
                zone_id=zone_config["name"],
                name=zone_config["name"].replace("_", " ").title(),
                area_sqm=zone_config["area_sqm"],
                crop_type=zone_config["crop_type"],
                soil_type=zone_config["soil_type"],
                sensors=[f"{zone_config['name']}_moisture_10cm", f"{zone_config['name']}_environment"],
                valve_id=f"valve_{i+1}",
                pump_id=f"pump_{i+1}",
                target_moisture_min=35.0,
                target_moisture_max=65.0,
                max_irrigation_duration=45,
                min_interval_hours=4
            )
            zones.append(zone)
        
        return zones
    
    def _setup_processing_rules(self):
        """Setup data processing rules"""
        
        # Low moisture alert rule
        low_moisture_rule = ProcessingRule(
            rule_id="low_moisture_alert",
            sensor_types=["SoilMoistureSensor"],
            condition="low_moisture",
            action="send_alert",
            parameters={"threshold": 25.0, "severity": "medium"}
        )
        self.data_processor.add_processing_rule(low_moisture_rule)
        
        # High temperature alert rule
        high_temp_rule = ProcessingRule(
            rule_id="high_temperature_alert",
            sensor_types=["TemperatureSensor"],
            condition="high_temperature",
            action="send_alert",
            parameters={"threshold": 35.0, "severity": "high"}
        )
        self.data_processor.add_processing_rule(high_temp_rule)
        
        # Anomaly detection rule
        anomaly_rule = ProcessingRule(
            rule_id="anomaly_detection",
            sensor_types=["SoilMoistureSensor", "TemperatureSensor"],
            condition="anomaly_detected",
            action="investigate",
            parameters={"auto_disable": False}
        )
        self.data_processor.add_processing_rule(anomaly_rule)
    
    async def _handle_sensor_data(self, network_data: Dict[str, Any]):
        """Handle sensor data from the sensor network"""
        self.message_count += 1
        
        # Process each sensor reading
        for sensor_id, sensor_data in network_data.get("sensors", {}).items():
            if "error" in sensor_data:
                logger.warning(f"Sensor error for {sensor_id}: {sensor_data['error']}")
                continue
            
            # Create structured sensor reading
            reading = SensorReading(
                sensor_id=sensor_data["sensor_id"],
                sensor_type=sensor_data["sensor_type"],
                timestamp=datetime.fromisoformat(sensor_data["timestamp"]),
                value=sensor_data["value"],
                unit=sensor_data["unit"],
                location=sensor_data["location"],
                metadata=sensor_data
            )
            
            # Process through data pipeline
            processed_result = self.data_processor.process_reading(reading)
            
            # Send to MQTT if connected
            if self.mqtt_client and self.mqtt_client.is_connected:
                topic = self.message_router.create_sensor_topic(
                    zone_id=self._get_zone_from_sensor(sensor_id),
                    device_id=sensor_id
                )
                message = self.message_router.format_sensor_message(sensor_data)
                self.mqtt_client.publish(topic, message)
            
            # Store processed data (mock)
            await self._store_processed_data(processed_result)
            
            # Update irrigation controller with soil moisture data
            if "moisture" in sensor_id and processed_result["status"] == "processed":
                await self._update_irrigation_controller(sensor_id, processed_result)
        
        # Log system status periodically
        if self.message_count % 10 == 0:
            await self._log_system_status()
    
    def _get_zone_from_sensor(self, sensor_id: str) -> str:
        """Extract zone ID from sensor ID"""
        for zone_config in self.farm_config["zones"]:
            if sensor_id.startswith(zone_config["name"]):
                return zone_config["name"]
        return "unknown"
    
    async def _store_processed_data(self, processed_data: Dict[str, Any]):
        """Store processed data (mock implementation for demo)"""
        # In a real system, this would store to actual databases
        logger.debug(f"Storing processed data: {processed_data.get('reading', {}).get('sensor_id', 'unknown')}")
    
    async def _update_irrigation_controller(self, sensor_id: str, processed_data: Dict[str, Any]):
        """Update irrigation controller with sensor data"""
        zone_id = self._get_zone_from_sensor(sensor_id)
        
        if zone_id in self.irrigation_controller.zones:
            zone = self.irrigation_controller.zones[zone_id]
            reading = processed_data.get("reading")
            
            if reading and hasattr(reading, 'value'):
                zone.current_moisture = reading.value
            elif reading and isinstance(reading, dict):
                zone.current_moisture = reading.get("value", zone.current_moisture)
    
    async def _handle_sensor_message(self, message_type: MessageType, topic: str, payload: Dict[str, Any]):
        """Handle sensor data messages from MQTT"""
        logger.debug(f"Received sensor message on {topic}")
    
    async def _handle_control_message(self, message_type: MessageType, topic: str, payload: Dict[str, Any]):
        """Handle control command messages from MQTT"""
        logger.info(f"Received control command: {payload}")
        
        command = payload.get("command")
        target = payload.get("target")
        
        if command == "start_irrigation" and target:
            duration = payload.get("parameters", {}).get("duration", 30)
            await self.irrigation_controller.manual_start_irrigation(target, duration)
        elif command == "stop_irrigation" and target:
            await self.irrigation_controller.manual_stop_irrigation(target)
    
    async def _control_valve(self, valve_id: str, state: bool):
        """Simulate valve control"""
        action = "OPEN" if state else "CLOSE"
        logger.info(f"🚿 Valve {valve_id}: {action}")
    
    async def _control_pump(self, pump_id: str, state: bool):
        """Simulate pump control"""
        action = "START" if state else "STOP"
        logger.info(f"⚡ Pump {pump_id}: {action}")
    
    async def _send_notification(self, notification: Dict[str, Any]):
        """Send system notification"""
        logger.info(f"📢 Notification: {notification}")
    
    async def _log_system_status(self):
        """Log comprehensive system status"""
        uptime = datetime.now() - self.demo_start_time if self.demo_start_time else timedelta(0)
        
        # Get system statistics
        network_stats = self.sensor_network.get_network_stats()
        processing_stats = self.data_processor.get_statistics()
        irrigation_status = self.irrigation_controller.get_system_status()
        
        logger.info("📊 SYSTEM STATUS:")
        logger.info(f"   ⏱️  Uptime: {uptime}")
        logger.info(f"   📡 Sensors: {network_stats['active_sensors']}/{network_stats['total_sensors']} active")
        logger.info(f"   📈 Success Rate: {network_stats['success_rate']:.1f}%")
        logger.info(f"   ⚙️  Processed: {processing_stats['processing_stats']['total_processed']} readings")
        logger.info(f"   🚿 Irrigation: {irrigation_status['active_irrigations']} zones active")
        logger.info(f"   🤖 ML Model: {'Trained' if irrigation_status['ml_model_trained'] else 'Learning'}")
    
    async def start_demo(self, duration_minutes: int = 60):
        """Start the complete IoT system demo"""
        if self.is_running:
            logger.warning("Demo is already running")
            return
        
        self.is_running = True
        self.demo_start_time = datetime.now()
        
        logger.info(f"🎬 Starting IoT Smart Irrigation Demo for {duration_minutes} minutes...")
        
        try:
            # Start all system components
            tasks = [
                self.sensor_network.start_monitoring(),
                self.irrigation_controller.start_control_loop(),
                self._demo_scenario_runner(duration_minutes)
            ]
            
            # Run all tasks concurrently
            await asyncio.gather(*tasks)
            
        except KeyboardInterrupt:
            logger.info("Demo interrupted by user")
        except Exception as e:
            logger.error(f"Demo error: {e}")
        finally:
            await self.stop_demo()
    
    async def _demo_scenario_runner(self, duration_minutes: int):
        """Run demo scenarios to showcase system capabilities"""
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        scenario_interval = max(60, duration_minutes * 60 // 10)  # 10 scenarios max
        
        scenarios = [
            self._scenario_normal_operation,
            self._scenario_low_moisture_alert,
            self._scenario_manual_override,
            self._scenario_weather_adjustment,
            self._scenario_sensor_failure_simulation
        ]
        
        scenario_index = 0
        
        while datetime.now() < end_time and self.is_running:
            # Run next scenario
            scenario = scenarios[scenario_index % len(scenarios)]
            logger.info(f"🎭 Running scenario: {scenario.__name__}")
            
            try:
                await scenario()
            except Exception as e:
                logger.error(f"Scenario error: {e}")
            
            scenario_index += 1
            await asyncio.sleep(scenario_interval)
        
        logger.info("🎬 Demo scenarios completed")
    
    async def _scenario_normal_operation(self):
        """Scenario: Normal system operation"""
        logger.info("📋 Scenario: Normal automatic irrigation operation")
        self.irrigation_controller.set_mode(IrrigationMode.AUTOMATIC)
    
    async def _scenario_low_moisture_alert(self):
        """Scenario: Simulate low moisture condition"""
        logger.info("📋 Scenario: Low moisture alert and automatic irrigation")
        
        # Simulate low moisture in zone_north
        zone = self.irrigation_controller.zones.get("zone_north")
        if zone:
            zone.current_moisture = 20.0  # Below threshold
    
    async def _scenario_manual_override(self):
        """Scenario: Manual irrigation override"""
        logger.info("📋 Scenario: Manual irrigation override")
        
        # Switch to manual mode and start irrigation
        self.irrigation_controller.set_mode(IrrigationMode.MANUAL)
        await self.irrigation_controller.manual_start_irrigation("zone_south", 15)
        
        # Wait a bit then switch back to automatic
        await asyncio.sleep(30)
        self.irrigation_controller.set_mode(IrrigationMode.AUTOMATIC)
    
    async def _scenario_weather_adjustment(self):
        """Scenario: Weather-based irrigation adjustment"""
        logger.info("📋 Scenario: Weather-based irrigation adjustment")
        
        # Simulate rain forecast
        self.irrigation_controller.weather.current_weather["forecast_rain_24h"] = 10.0
        
        # Reset after a while
        await asyncio.sleep(60)
        self.irrigation_controller.weather.current_weather["forecast_rain_24h"] = 0.0
    
    async def _scenario_sensor_failure_simulation(self):
        """Scenario: Sensor failure and recovery"""
        logger.info("📋 Scenario: Sensor failure simulation")
        
        # Disable a sensor temporarily
        sensor_id = "zone_north_moisture_10cm"
        if sensor_id in self.sensor_network.sensors:
            self.sensor_network.set_sensor_active(sensor_id, False)
            
            # Re-enable after 2 minutes
            await asyncio.sleep(120)
            self.sensor_network.set_sensor_active(sensor_id, True)
    
    async def stop_demo(self):
        """Stop the demo and cleanup"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("🛑 Stopping IoT Smart Irrigation Demo...")
        
        # Stop all components
        if self.sensor_network:
            self.sensor_network.stop_monitoring()
        
        if self.irrigation_controller:
            self.irrigation_controller.stop_control_loop()
        
        if self.mqtt_client:
            self.mqtt_client.disconnect()
        
        # Final status report
        await self._log_system_status()
        
        logger.info("✅ Demo stopped successfully")


async def main():
    """Main demo entry point"""
    demo = IoTIrrigationDemo()
    
    try:
        await demo.initialize_system()
        await demo.start_demo(duration_minutes=30)  # 30-minute demo
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}")
    finally:
        await demo.stop_demo()


if __name__ == "__main__":
    print("🌱 IoT Smart Irrigation System - Complete Demo")
    print("=" * 50)
    print("This demo showcases a complete IoT ecosystem including:")
    print("• Realistic sensor simulation with environmental patterns")
    print("• MQTT communication with proper topic structure")
    print("• Real-time data processing and anomaly detection")
    print("• Intelligent irrigation control with ML optimization")
    print("• Comprehensive system monitoring and alerts")
    print("=" * 50)
    print("Press Ctrl+C to stop the demo at any time")
    print()
    
    asyncio.run(main())
