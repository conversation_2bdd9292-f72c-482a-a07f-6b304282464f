"""
Intelligent Irrigation Control System
Implements rule-based and ML-based decision making for optimal water management
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import json
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler

logger = logging.getLogger(__name__)


class IrrigationMode(Enum):
    """Irrigation control modes"""
    MANUAL = "manual"
    AUTOMATIC = "automatic"
    SCHEDULED = "scheduled"
    EMERGENCY = "emergency"
    MAINTENANCE = "maintenance"


class ZoneStatus(Enum):
    """Zone irrigation status"""
    IDLE = "idle"
    IRRIGATING = "irrigating"
    PAUSED = "paused"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class IrrigationZone:
    """Irrigation zone configuration and state"""
    zone_id: str
    name: str
    area_sqm: float
    crop_type: str
    soil_type: str
    sensors: List[str]  # List of sensor IDs
    valve_id: str
    pump_id: Optional[str] = None
    
    # Control parameters
    target_moisture_min: float = 30.0  # %
    target_moisture_max: float = 70.0  # %
    max_irrigation_duration: int = 60  # minutes
    min_interval_hours: int = 6
    
    # Current state
    status: ZoneStatus = ZoneStatus.IDLE
    last_irrigation: Optional[datetime] = None
    current_moisture: float = 0.0
    irrigation_start_time: Optional[datetime] = None
    total_water_used: float = 0.0  # liters
    
    def needs_irrigation(self) -> bool:
        """Check if zone needs irrigation based on moisture level"""
        return self.current_moisture < self.target_moisture_min
    
    def can_irrigate(self) -> bool:
        """Check if zone can be irrigated (respects minimum interval)"""
        if self.last_irrigation is None:
            return True
        
        time_since_last = datetime.now() - self.last_irrigation
        return time_since_last.total_seconds() >= (self.min_interval_hours * 3600)
    
    def is_over_irrigated(self) -> bool:
        """Check if zone is over-irrigated"""
        return self.current_moisture > self.target_moisture_max


@dataclass
class IrrigationDecision:
    """Irrigation decision with reasoning"""
    zone_id: str
    action: str  # "start", "stop", "continue", "skip"
    duration_minutes: int
    reason: str
    confidence: float
    sensor_data: Dict[str, Any]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging/storage"""
        return {
            "zone_id": self.zone_id,
            "action": self.action,
            "duration_minutes": self.duration_minutes,
            "reason": self.reason,
            "confidence": self.confidence,
            "sensor_data": self.sensor_data,
            "timestamp": self.timestamp.isoformat()
        }


class WeatherIntegration:
    """Weather data integration for irrigation decisions"""
    
    def __init__(self):
        self.current_weather = {
            "temperature": 25.0,
            "humidity": 60.0,
            "wind_speed": 5.0,
            "precipitation_mm": 0.0,
            "forecast_rain_24h": 0.0,
            "evapotranspiration": 5.0  # mm/day
        }
    
    def get_evapotranspiration_rate(self, crop_type: str) -> float:
        """Calculate crop-specific evapotranspiration rate"""
        # Crop coefficients (simplified)
        crop_coefficients = {
            "tomato": 1.15,
            "lettuce": 0.8,
            "corn": 1.2,
            "wheat": 1.0,
            "grass": 0.9,
            "default": 1.0
        }
        
        kc = crop_coefficients.get(crop_type.lower(), 1.0)
        return self.current_weather["evapotranspiration"] * kc
    
    def should_skip_irrigation_due_to_rain(self) -> bool:
        """Check if irrigation should be skipped due to rain forecast"""
        return self.current_weather["forecast_rain_24h"] > 5.0  # mm


class MLIrrigationPredictor:
    """Machine learning model for irrigation optimization"""
    
    def __init__(self):
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_names = [
            "current_moisture", "temperature", "humidity", "light_intensity",
            "days_since_last_irrigation", "crop_coefficient", "soil_retention"
        ]
    
    def prepare_features(self, zone: IrrigationZone, sensor_data: Dict[str, Any], weather: Dict[str, Any]) -> np.ndarray:
        """Prepare features for ML model"""
        # Days since last irrigation
        days_since_irrigation = 0
        if zone.last_irrigation:
            days_since_irrigation = (datetime.now() - zone.last_irrigation).days
        
        # Crop coefficient (simplified)
        crop_coefficients = {"tomato": 1.15, "lettuce": 0.8, "corn": 1.2, "default": 1.0}
        crop_coeff = crop_coefficients.get(zone.crop_type.lower(), 1.0)
        
        # Soil water retention (simplified)
        soil_retention = {"clay": 0.8, "loam": 0.6, "sand": 0.3, "default": 0.5}
        soil_ret = soil_retention.get(zone.soil_type.lower(), 0.5)
        
        features = [
            zone.current_moisture,
            sensor_data.get("temperature", 25.0),
            sensor_data.get("humidity", 60.0),
            sensor_data.get("light_intensity", 50000),
            days_since_irrigation,
            crop_coeff,
            soil_ret
        ]
        
        return np.array(features).reshape(1, -1)
    
    def predict_irrigation_duration(self, zone: IrrigationZone, sensor_data: Dict[str, Any], weather: Dict[str, Any]) -> int:
        """Predict optimal irrigation duration in minutes"""
        if not self.is_trained:
            # Use rule-based fallback if model not trained
            return self._rule_based_duration(zone, sensor_data)
        
        try:
            features = self.prepare_features(zone, sensor_data, weather)
            features_scaled = self.scaler.transform(features)
            duration = self.model.predict(features_scaled)[0]
            
            # Ensure reasonable bounds
            return max(5, min(int(duration), zone.max_irrigation_duration))
            
        except Exception as e:
            logger.error(f"Error in ML prediction: {e}")
            return self._rule_based_duration(zone, sensor_data)
    
    def _rule_based_duration(self, zone: IrrigationZone, sensor_data: Dict[str, Any]) -> int:
        """Rule-based irrigation duration calculation"""
        moisture_deficit = zone.target_moisture_max - zone.current_moisture
        
        # Base duration on moisture deficit
        base_duration = max(10, moisture_deficit * 0.5)  # 0.5 min per % moisture
        
        # Adjust for temperature (higher temp = longer irrigation)
        temp_factor = 1.0 + (sensor_data.get("temperature", 25) - 25) * 0.02
        
        # Adjust for soil type
        soil_factors = {"clay": 1.2, "loam": 1.0, "sand": 0.8}
        soil_factor = soil_factors.get(zone.soil_type.lower(), 1.0)
        
        duration = base_duration * temp_factor * soil_factor
        return max(5, min(int(duration), zone.max_irrigation_duration))
    
    def train_model(self, training_data: List[Dict[str, Any]]):
        """Train the ML model with historical data"""
        if len(training_data) < 50:  # Need minimum data for training
            logger.warning("Insufficient training data for ML model")
            return
        
        try:
            # Prepare training data
            X = []
            y = []
            
            for record in training_data:
                features = [
                    record["moisture_before"],
                    record["temperature"],
                    record["humidity"],
                    record["light_intensity"],
                    record["days_since_last"],
                    record["crop_coefficient"],
                    record["soil_retention"]
                ]
                X.append(features)
                y.append(record["irrigation_duration"])
            
            X = np.array(X)
            y = np.array(y)
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train model
            self.model.fit(X_scaled, y)
            self.is_trained = True
            
            logger.info(f"ML model trained with {len(training_data)} samples")
            
        except Exception as e:
            logger.error(f"Error training ML model: {e}")


class IrrigationController:
    """Main irrigation control system"""
    
    def __init__(self, zones: List[IrrigationZone]):
        self.zones = {zone.zone_id: zone for zone in zones}
        self.mode = IrrigationMode.AUTOMATIC
        self.weather = WeatherIntegration()
        self.ml_predictor = MLIrrigationPredictor()
        
        # Control state
        self.is_running = False
        self.decision_interval = 300  # seconds (5 minutes)
        self.emergency_stop = False
        
        # Callbacks for hardware control
        self.valve_control_callback: Optional[Callable] = None
        self.pump_control_callback: Optional[Callable] = None
        self.notification_callback: Optional[Callable] = None
        
        # Decision history
        self.decision_history: List[IrrigationDecision] = []
        self.max_history_size = 1000
        
        # Statistics
        self.stats = {
            "total_decisions": 0,
            "irrigations_started": 0,
            "irrigations_completed": 0,
            "water_saved_liters": 0,
            "ml_predictions": 0,
            "rule_based_decisions": 0
        }
    
    def set_valve_control_callback(self, callback: Callable):
        """Set callback for valve control"""
        self.valve_control_callback = callback
    
    def set_pump_control_callback(self, callback: Callable):
        """Set callback for pump control"""
        self.pump_control_callback = callback
    
    def set_notification_callback(self, callback: Callable):
        """Set callback for notifications"""
        self.notification_callback = callback
    
    async def start_control_loop(self):
        """Start the main control loop"""
        if self.is_running:
            logger.warning("Control loop already running")
            return
        
        self.is_running = True
        logger.info("Starting irrigation control loop")
        
        try:
            while self.is_running and not self.emergency_stop:
                await self._control_cycle()
                await asyncio.sleep(self.decision_interval)
        except Exception as e:
            logger.error(f"Error in control loop: {e}")
        finally:
            self.is_running = False
            logger.info("Irrigation control loop stopped")
    
    def stop_control_loop(self):
        """Stop the control loop"""
        self.is_running = False
        logger.info("Stopping irrigation control loop")
    
    def emergency_stop_all(self):
        """Emergency stop all irrigation"""
        self.emergency_stop = True
        
        for zone in self.zones.values():
            if zone.status == ZoneStatus.IRRIGATING:
                asyncio.create_task(self._stop_zone_irrigation(zone.zone_id, "emergency_stop"))
        
        logger.warning("Emergency stop activated - all irrigation stopped")
        
        if self.notification_callback:
            asyncio.create_task(self.notification_callback({
                "type": "emergency_stop",
                "message": "Emergency stop activated - all irrigation stopped",
                "timestamp": datetime.now().isoformat()
            }))
    
    async def _control_cycle(self):
        """Single control cycle - make decisions for all zones"""
        logger.debug("Starting control cycle")
        
        for zone_id, zone in self.zones.items():
            try:
                # Skip if in maintenance mode
                if zone.status == ZoneStatus.DISABLED:
                    continue
                
                # Get latest sensor data for this zone
                sensor_data = await self._get_zone_sensor_data(zone_id)
                
                # Update zone moisture level
                zone.current_moisture = sensor_data.get("soil_moisture", zone.current_moisture)
                
                # Make irrigation decision
                decision = await self._make_irrigation_decision(zone, sensor_data)
                
                # Execute decision
                await self._execute_decision(decision)
                
                # Record decision
                self._record_decision(decision)
                
            except Exception as e:
                logger.error(f"Error in control cycle for zone {zone_id}: {e}")
    
    async def _get_zone_sensor_data(self, zone_id: str) -> Dict[str, Any]:
        """Get latest sensor data for a zone"""
        # This would typically fetch from the data storage system
        # For simulation, return mock data
        return {
            "soil_moisture": 45.0,
            "temperature": 26.5,
            "humidity": 65.0,
            "light_intensity": 45000,
            "ph": 6.8,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _make_irrigation_decision(self, zone: IrrigationZone, sensor_data: Dict[str, Any]) -> IrrigationDecision:
        """Make irrigation decision for a zone"""
        self.stats["total_decisions"] += 1
        
        # Check if manual mode
        if self.mode == IrrigationMode.MANUAL:
            return IrrigationDecision(
                zone_id=zone.zone_id,
                action="skip",
                duration_minutes=0,
                reason="manual_mode",
                confidence=1.0,
                sensor_data=sensor_data,
                timestamp=datetime.now()
            )
        
        # Check emergency conditions
        if self.emergency_stop:
            return IrrigationDecision(
                zone_id=zone.zone_id,
                action="stop",
                duration_minutes=0,
                reason="emergency_stop",
                confidence=1.0,
                sensor_data=sensor_data,
                timestamp=datetime.now()
            )
        
        # Check if currently irrigating
        if zone.status == ZoneStatus.IRRIGATING:
            return await self._decide_continue_or_stop(zone, sensor_data)
        
        # Check if irrigation is needed
        if not zone.needs_irrigation():
            return IrrigationDecision(
                zone_id=zone.zone_id,
                action="skip",
                duration_minutes=0,
                reason="moisture_sufficient",
                confidence=0.9,
                sensor_data=sensor_data,
                timestamp=datetime.now()
            )
        
        # Check if can irrigate (minimum interval)
        if not zone.can_irrigate():
            return IrrigationDecision(
                zone_id=zone.zone_id,
                action="skip",
                duration_minutes=0,
                reason="minimum_interval_not_met",
                confidence=0.8,
                sensor_data=sensor_data,
                timestamp=datetime.now()
            )
        
        # Check weather conditions
        if self.weather.should_skip_irrigation_due_to_rain():
            return IrrigationDecision(
                zone_id=zone.zone_id,
                action="skip",
                duration_minutes=0,
                reason="rain_forecast",
                confidence=0.9,
                sensor_data=sensor_data,
                timestamp=datetime.now()
            )
        
        # Decide to start irrigation
        duration = self.ml_predictor.predict_irrigation_duration(
            zone, sensor_data, self.weather.current_weather
        )
        
        if self.ml_predictor.is_trained:
            self.stats["ml_predictions"] += 1
            confidence = 0.85
        else:
            self.stats["rule_based_decisions"] += 1
            confidence = 0.75
        
        return IrrigationDecision(
            zone_id=zone.zone_id,
            action="start",
            duration_minutes=duration,
            reason="moisture_low_start_irrigation",
            confidence=confidence,
            sensor_data=sensor_data,
            timestamp=datetime.now()
        )

    async def _decide_continue_or_stop(self, zone: IrrigationZone, sensor_data: Dict[str, Any]) -> IrrigationDecision:
        """Decide whether to continue or stop ongoing irrigation"""

        # Check if over-irrigated
        if zone.is_over_irrigated():
            return IrrigationDecision(
                zone_id=zone.zone_id,
                action="stop",
                duration_minutes=0,
                reason="target_moisture_reached",
                confidence=0.9,
                sensor_data=sensor_data,
                timestamp=datetime.now()
            )

        # Check maximum duration
        if zone.irrigation_start_time:
            elapsed = (datetime.now() - zone.irrigation_start_time).total_seconds() / 60
            if elapsed >= zone.max_irrigation_duration:
                return IrrigationDecision(
                    zone_id=zone.zone_id,
                    action="stop",
                    duration_minutes=0,
                    reason="max_duration_reached",
                    confidence=1.0,
                    sensor_data=sensor_data,
                    timestamp=datetime.now()
                )

        # Continue irrigation
        return IrrigationDecision(
            zone_id=zone.zone_id,
            action="continue",
            duration_minutes=0,
            reason="irrigation_in_progress",
            confidence=0.8,
            sensor_data=sensor_data,
            timestamp=datetime.now()
        )

    async def _execute_decision(self, decision: IrrigationDecision):
        """Execute irrigation decision"""
        zone = self.zones[decision.zone_id]

        if decision.action == "start":
            await self._start_zone_irrigation(decision.zone_id, decision.duration_minutes)
            self.stats["irrigations_started"] += 1

        elif decision.action == "stop":
            await self._stop_zone_irrigation(decision.zone_id, decision.reason)
            if zone.status == ZoneStatus.IRRIGATING:
                self.stats["irrigations_completed"] += 1

        logger.info(f"Zone {decision.zone_id}: {decision.action} - {decision.reason}")

    async def _start_zone_irrigation(self, zone_id: str, duration_minutes: int):
        """Start irrigation for a zone"""
        zone = self.zones[zone_id]

        # Update zone state
        zone.status = ZoneStatus.IRRIGATING
        zone.irrigation_start_time = datetime.now()

        # Control hardware
        if self.valve_control_callback:
            await self.valve_control_callback(zone.valve_id, True)

        if zone.pump_id and self.pump_control_callback:
            await self.pump_control_callback(zone.pump_id, True)

        # Schedule automatic stop
        asyncio.create_task(self._auto_stop_irrigation(zone_id, duration_minutes))

        # Send notification
        if self.notification_callback:
            await self.notification_callback({
                "type": "irrigation_started",
                "zone_id": zone_id,
                "duration_minutes": duration_minutes,
                "timestamp": datetime.now().isoformat()
            })

        logger.info(f"Started irrigation for zone {zone_id} for {duration_minutes} minutes")

    async def _stop_zone_irrigation(self, zone_id: str, reason: str):
        """Stop irrigation for a zone"""
        zone = self.zones[zone_id]

        if zone.status != ZoneStatus.IRRIGATING:
            return

        # Calculate water usage
        if zone.irrigation_start_time:
            duration_hours = (datetime.now() - zone.irrigation_start_time).total_seconds() / 3600
            water_used = duration_hours * 300  # Assume 300 L/hour flow rate
            zone.total_water_used += water_used

        # Update zone state
        zone.status = ZoneStatus.IDLE
        zone.last_irrigation = datetime.now()
        zone.irrigation_start_time = None

        # Control hardware
        if self.valve_control_callback:
            await self.valve_control_callback(zone.valve_id, False)

        if zone.pump_id and self.pump_control_callback:
            await self.pump_control_callback(zone.pump_id, False)

        # Send notification
        if self.notification_callback:
            await self.notification_callback({
                "type": "irrigation_stopped",
                "zone_id": zone_id,
                "reason": reason,
                "timestamp": datetime.now().isoformat()
            })

        logger.info(f"Stopped irrigation for zone {zone_id} - {reason}")

    async def _auto_stop_irrigation(self, zone_id: str, duration_minutes: int):
        """Automatically stop irrigation after duration"""
        await asyncio.sleep(duration_minutes * 60)

        zone = self.zones[zone_id]
        if zone.status == ZoneStatus.IRRIGATING:
            await self._stop_zone_irrigation(zone_id, "scheduled_duration_complete")

    def _record_decision(self, decision: IrrigationDecision):
        """Record decision in history"""
        self.decision_history.append(decision)

        # Maintain history size
        if len(self.decision_history) > self.max_history_size:
            self.decision_history = self.decision_history[-self.max_history_size:]

    def get_zone_status(self, zone_id: str) -> Dict[str, Any]:
        """Get current status of a zone"""
        if zone_id not in self.zones:
            return {"error": "Zone not found"}

        zone = self.zones[zone_id]
        return {
            "zone_id": zone.zone_id,
            "name": zone.name,
            "status": zone.status.value,
            "current_moisture": zone.current_moisture,
            "target_moisture_min": zone.target_moisture_min,
            "target_moisture_max": zone.target_moisture_max,
            "last_irrigation": zone.last_irrigation.isoformat() if zone.last_irrigation else None,
            "irrigation_start_time": zone.irrigation_start_time.isoformat() if zone.irrigation_start_time else None,
            "total_water_used": zone.total_water_used,
            "needs_irrigation": zone.needs_irrigation(),
            "can_irrigate": zone.can_irrigate()
        }

    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            "mode": self.mode.value,
            "is_running": self.is_running,
            "emergency_stop": self.emergency_stop,
            "total_zones": len(self.zones),
            "active_irrigations": len([z for z in self.zones.values() if z.status == ZoneStatus.IRRIGATING]),
            "statistics": self.stats.copy(),
            "ml_model_trained": self.ml_predictor.is_trained
        }

    def get_recent_decisions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent irrigation decisions"""
        recent = self.decision_history[-limit:] if limit else self.decision_history
        return [decision.to_dict() for decision in recent]

    def set_mode(self, mode: IrrigationMode):
        """Set irrigation control mode"""
        old_mode = self.mode
        self.mode = mode
        logger.info(f"Irrigation mode changed from {old_mode.value} to {mode.value}")

        # If switching to manual, stop all automatic irrigation
        if mode == IrrigationMode.MANUAL:
            for zone in self.zones.values():
                if zone.status == ZoneStatus.IRRIGATING:
                    asyncio.create_task(self._stop_zone_irrigation(zone.zone_id, "switched_to_manual"))

    async def manual_start_irrigation(self, zone_id: str, duration_minutes: int) -> bool:
        """Manually start irrigation for a zone"""
        if zone_id not in self.zones:
            return False

        zone = self.zones[zone_id]

        if zone.status == ZoneStatus.IRRIGATING:
            logger.warning(f"Zone {zone_id} is already irrigating")
            return False

        await self._start_zone_irrigation(zone_id, duration_minutes)

        # Record manual decision
        decision = IrrigationDecision(
            zone_id=zone_id,
            action="start",
            duration_minutes=duration_minutes,
            reason="manual_override",
            confidence=1.0,
            sensor_data={},
            timestamp=datetime.now()
        )
        self._record_decision(decision)

        return True

    async def manual_stop_irrigation(self, zone_id: str) -> bool:
        """Manually stop irrigation for a zone"""
        if zone_id not in self.zones:
            return False

        await self._stop_zone_irrigation(zone_id, "manual_stop")

        # Record manual decision
        decision = IrrigationDecision(
            zone_id=zone_id,
            action="stop",
            duration_minutes=0,
            reason="manual_stop",
            confidence=1.0,
            sensor_data={},
            timestamp=datetime.now()
        )
        self._record_decision(decision)

        return True
