# IoT Smart Irrigation System - Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB RAM and 10GB disk space
- Network access for downloading images

### One-Command Deployment
```bash
chmod +x deployment/start.sh
./deployment/start.sh
```

This will automatically:
- Set up all required services
- Configure databases and message brokers
- Start the complete IoT platform
- Display access URLs and credentials

## 📋 Manual Deployment

### 1. Environment Setup
```bash
# Clone the repository
git clone <repository-url>
cd IOT_Pro

# Create environment file
cp .env.example .env
# Edit .env with your configuration
```

### 2. Start Infrastructure Services
```bash
# Start databases and message broker
docker-compose up -d mosquitto mongodb influxdb redis

# Wait for services to initialize
sleep 30
```

### 3. Initialize Databases
```bash
# MongoDB will auto-initialize with the provided script
# InfluxDB will create the bucket automatically
# Check service logs if needed
docker-compose logs influxdb
```

### 4. Start Application Services
```bash
# Start the main application
docker-compose up -d irrigation_app

# Start monitoring and visualization
docker-compose up -d grafana prometheus node_exporter

# Start reverse proxy
docker-compose up -d nginx
```

### 5. Verify Deployment
```bash
# Check all services are running
docker-compose ps

# Check application health
curl http://localhost/api/status

# View logs
docker-compose logs -f irrigation_app
```

## 🔧 Configuration

### Environment Variables
Key environment variables in `.env`:

```bash
# Database URLs
MONGODB_URL=***************************************************
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=your-influxdb-token

# Security (CHANGE IN PRODUCTION!)
SECRET_KEY=your-super-secret-key
JWT_SECRET=your-jwt-secret

# MQTT Configuration
MQTT_BROKER=mosquitto
MQTT_PORT=1883

# Email Notifications (Optional)
EMAIL_ENABLED=true
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

### System Settings
Configure system settings through the web interface:
1. Access http://localhost
2. Login with admin/admin123
3. Go to Settings → System Configuration
4. Update farm details, thresholds, and preferences

### Device Registration
Register IoT devices through the API or web interface:
```bash
curl -X POST http://localhost/api/devices \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "sensor_001",
    "name": "North Field Moisture Sensor",
    "device_type": "sensor",
    "location": {"lat": 40.7128, "lng": -74.0060},
    "zone_id": "zone_north"
  }'
```

## 🔒 Security Configuration

### 1. Change Default Passwords
```bash
# Application admin password
# Login to web interface and change password

# Database passwords
# Update docker-compose.yml with secure passwords

# Regenerate secrets
openssl rand -hex 32  # For SECRET_KEY
openssl rand -hex 32  # For JWT_SECRET
```

### 2. Enable HTTPS (Production)
```bash
# Generate SSL certificates
mkdir -p deployment/nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout deployment/nginx/ssl/nginx.key \
  -out deployment/nginx/ssl/nginx.crt

# Update nginx.conf to enable HTTPS
# Uncomment SSL configuration in nginx.conf
```

### 3. Network Security
```bash
# Restrict database access
# Update docker-compose.yml to remove exposed ports for databases
# Use internal network communication only

# Enable firewall
sudo ufw enable
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp  # SSH only
```

## 📊 Monitoring and Maintenance

### Health Checks
```bash
# Application health
curl http://localhost/api/status

# Database connectivity
docker-compose exec mongodb mongosh --eval "db.runCommand('ping')"
docker-compose exec influxdb influx ping

# MQTT broker
mosquitto_pub -h localhost -t test/topic -m "test message"
```

### Log Management
```bash
# View application logs
docker-compose logs -f irrigation_app

# View all service logs
docker-compose logs -f

# Log rotation (add to crontab)
0 2 * * * docker-compose exec irrigation_app find /app/logs -name "*.log" -mtime +7 -delete
```

### Backup Procedures
```bash
# Database backup
docker-compose exec mongodb mongodump --out /backup/mongodb
docker-compose exec influxdb influx backup /backup/influxdb

# Configuration backup
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/

# Full system backup
./deployment/backup.sh
```

### Updates and Maintenance
```bash
# Update application
git pull
docker-compose build irrigation_app
docker-compose up -d irrigation_app

# Update all services
docker-compose pull
docker-compose up -d

# Database maintenance
docker-compose exec mongodb mongosh --eval "db.runCommand('compact')"
```

## 🔧 Troubleshooting

### Common Issues

#### Services Won't Start
```bash
# Check Docker daemon
sudo systemctl status docker

# Check available resources
docker system df
docker system prune  # Clean up if needed

# Check port conflicts
netstat -tulpn | grep :8000
```

#### Database Connection Issues
```bash
# Check database logs
docker-compose logs mongodb
docker-compose logs influxdb

# Test connectivity
docker-compose exec irrigation_app ping mongodb
docker-compose exec irrigation_app ping influxdb
```

#### MQTT Connection Problems
```bash
# Check MQTT broker
docker-compose logs mosquitto

# Test MQTT connectivity
mosquitto_sub -h localhost -t "irrigation/+/+/+"
```

#### Application Errors
```bash
# Check application logs
docker-compose logs irrigation_app

# Check configuration
docker-compose exec irrigation_app cat /app/config/settings.yaml

# Restart application
docker-compose restart irrigation_app
```

### Performance Optimization

#### Database Optimization
```bash
# MongoDB indexes
docker-compose exec mongodb mongosh irrigation_system --eval "
  db.sensors.createIndex({timestamp: -1});
  db.sensors.createIndex({sensor_id: 1, timestamp: -1});
"

# InfluxDB retention policies
docker-compose exec influxdb influx setup \
  --retention 90d \
  --shard-duration 1d
```

#### Application Tuning
```bash
# Increase worker processes
# Update docker-compose.yml:
# command: ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "main:app"]

# Optimize memory usage
# Set environment variables:
# PYTHONOPTIMIZE=1
# MALLOC_TRIM_THRESHOLD_=100000
```

## 🌐 Production Deployment

### Cloud Deployment (AWS/Azure/GCP)
1. Use managed databases (RDS, CosmosDB, Cloud SQL)
2. Deploy containers using ECS, AKS, or GKE
3. Use load balancers for high availability
4. Implement auto-scaling policies
5. Set up monitoring and alerting

### Edge Deployment
1. Use lightweight containers for edge devices
2. Implement local data caching
3. Configure offline operation modes
4. Set up data synchronization

### Scaling Considerations
- Horizontal scaling: Multiple application instances
- Database sharding for large datasets
- Message queue clustering for high throughput
- CDN for static content delivery

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review application logs
3. Consult the API documentation at `/docs`
4. Check GitHub issues and discussions

## 🔄 Maintenance Schedule

### Daily
- Monitor system health dashboards
- Check alert notifications
- Verify data collection rates

### Weekly
- Review system logs
- Check database performance
- Update security patches

### Monthly
- Full system backup
- Performance optimization review
- Capacity planning assessment

### Quarterly
- Security audit
- Dependency updates
- Disaster recovery testing
