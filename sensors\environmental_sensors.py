"""
Environmental Sensors Implementation
Temperature, Humidity, and Light sensors for IoT irrigation system
"""

import math
import random
from datetime import datetime, timedelta
from .base_sensor import BaseSensor


class TemperatureSensor(BaseSensor):
    """
    Temperature sensor simulation (DHT22/SHT30 style)
    Simulates realistic temperature patterns with daily and seasonal cycles
    """
    
    def __init__(self, sensor_id: str, location: dict, **kwargs):
        super().__init__(sensor_id, location, **kwargs)
        self.base_temperature = 22.0  # Base temperature in Celsius
        self.daily_variation = 8.0    # Daily temperature swing
        self.seasonal_variation = 15.0 # Seasonal variation
        
    def get_unit(self) -> str:
        return "°C"
    
    def read_raw_value(self) -> float:
        """Simulate realistic temperature with daily and seasonal cycles"""
        now = datetime.now()
        
        # Daily cycle (peak at 2 PM)
        hour_angle = (now.hour - 14) * math.pi / 12
        daily_temp = self.daily_variation * math.cos(hour_angle)
        
        # Seasonal cycle (peak in summer)
        day_of_year = now.timetuple().tm_yday
        seasonal_angle = (day_of_year - 172) * 2 * math.pi / 365  # Peak around June 21
        seasonal_temp = self.seasonal_variation * math.cos(seasonal_angle)
        
        # Random weather variations
        weather_variation = random.gauss(0, 2)
        
        return self.base_temperature + daily_temp + seasonal_temp + weather_variation


class HumiditySensor(BaseSensor):
    """
    Humidity sensor simulation
    Simulates relative humidity with realistic patterns
    """
    
    def __init__(self, sensor_id: str, location: dict, **kwargs):
        super().__init__(sensor_id, location, **kwargs)
        self.base_humidity = 60.0  # Base humidity percentage
        
    def get_unit(self) -> str:
        return "%RH"
    
    def read_raw_value(self) -> float:
        """Simulate realistic humidity patterns"""
        now = datetime.now()
        
        # Humidity typically higher at night, lower during day
        hour_angle = (now.hour - 14) * math.pi / 12
        daily_humidity = -15 * math.cos(hour_angle)  # Inverse of temperature
        
        # Seasonal humidity variation
        day_of_year = now.timetuple().tm_yday
        seasonal_angle = (day_of_year - 172) * 2 * math.pi / 365
        seasonal_humidity = -10 * math.cos(seasonal_angle)  # Lower in summer
        
        # Weather variations
        weather_variation = random.gauss(0, 5)
        
        humidity = self.base_humidity + daily_humidity + seasonal_humidity + weather_variation
        return max(10, min(95, humidity))  # Realistic bounds


class LightSensor(BaseSensor):
    """
    Light intensity sensor simulation (LDR/BH1750 style)
    Simulates realistic light patterns with day/night cycles
    """
    
    def __init__(self, sensor_id: str, location: dict, **kwargs):
        super().__init__(sensor_id, location, **kwargs)
        self.max_light = 100000  # Maximum lux (bright sunlight)
        
    def get_unit(self) -> str:
        return "lux"
    
    def read_raw_value(self) -> float:
        """Simulate realistic light intensity patterns"""
        now = datetime.now()
        
        # Solar elevation simulation
        hour_angle = (now.hour - 12) * math.pi / 12
        
        if -math.pi/2 <= hour_angle <= math.pi/2:  # Daytime
            # Cosine curve for sun elevation
            sun_elevation = math.cos(hour_angle)
            base_light = self.max_light * sun_elevation * sun_elevation
            
            # Cloud cover simulation (random reduction)
            cloud_factor = random.uniform(0.3, 1.0)
            light_intensity = base_light * cloud_factor
            
            # Add some noise
            light_intensity += random.gauss(0, light_intensity * 0.1)
            
        else:  # Nighttime
            light_intensity = random.uniform(0, 10)  # Moonlight/artificial light
        
        return max(0, light_intensity)


class WaterFlowSensor(BaseSensor):
    """
    Water flow sensor simulation (Hall effect flow meter)
    Measures water flow rate in irrigation system
    """
    
    def __init__(self, sensor_id: str, location: dict, **kwargs):
        super().__init__(sensor_id, location, **kwargs)
        self.is_flowing = False
        self.target_flow_rate = 0.0  # L/min
        self.current_flow_rate = 0.0
        
    def get_unit(self) -> str:
        return "L/min"
    
    def set_flow_state(self, flowing: bool, flow_rate: float = 5.0):
        """Control flow state (called by irrigation controller)"""
        self.is_flowing = flowing
        self.target_flow_rate = flow_rate if flowing else 0.0
    
    def read_raw_value(self) -> float:
        """Simulate water flow with realistic startup/shutdown behavior"""
        if self.is_flowing:
            # Gradual ramp up to target flow rate
            if self.current_flow_rate < self.target_flow_rate:
                self.current_flow_rate = min(
                    self.target_flow_rate,
                    self.current_flow_rate + 0.5  # Ramp rate
                )
        else:
            # Gradual ramp down
            self.current_flow_rate = max(0, self.current_flow_rate - 0.3)
        
        # Add pressure variations and noise
        pressure_variation = random.gauss(0, 0.1)
        flow_with_noise = self.current_flow_rate + pressure_variation
        
        return max(0, flow_with_noise)


class PHSensor(BaseSensor):
    """
    Soil pH sensor simulation
    Measures soil acidity/alkalinity
    """
    
    def __init__(self, sensor_id: str, location: dict, soil_type: str = "loam", **kwargs):
        super().__init__(sensor_id, location, **kwargs)
        self.soil_type = soil_type
        
        # Typical pH ranges for different soil types
        self.ph_ranges = {
            "sand": (6.0, 7.5),
            "clay": (6.5, 8.0),
            "loam": (6.0, 7.5),
            "silt": (6.2, 7.8)
        }
        
        # Set base pH for soil type
        ph_range = self.ph_ranges.get(soil_type, (6.0, 7.5))
        self.base_ph = random.uniform(*ph_range)
        
    def get_unit(self) -> str:
        return "pH"
    
    def read_raw_value(self) -> float:
        """Simulate pH readings with slow changes over time"""
        # pH changes slowly over time due to irrigation, fertilizers, etc.
        slow_drift = random.gauss(0, 0.01)  # Very slow changes
        
        # Occasional larger changes (fertilizer application, etc.)
        if random.random() < 0.001:  # 0.1% chance
            slow_drift += random.gauss(0, 0.2)
        
        self.base_ph += slow_drift
        
        # Keep within realistic bounds
        self.base_ph = max(4.0, min(9.0, self.base_ph))
        
        # Add measurement noise
        return self.base_ph + random.gauss(0, 0.05)


class CombinedEnvironmentalSensor(BaseSensor):
    """
    Combined environmental sensor (like BME280)
    Provides temperature, humidity, and pressure in one device
    """
    
    def __init__(self, sensor_id: str, location: dict, **kwargs):
        super().__init__(sensor_id, location, **kwargs)
        self.temp_sensor = TemperatureSensor(f"{sensor_id}_temp", location, **kwargs)
        self.humidity_sensor = HumiditySensor(f"{sensor_id}_humidity", location, **kwargs)
        self.base_pressure = 1013.25  # Sea level pressure in hPa
        
    def get_unit(self) -> str:
        return "multi"
    
    def read_raw_value(self) -> float:
        """This method returns pressure, but we override read() for multi-sensor data"""
        # Pressure varies with weather and altitude
        weather_variation = random.gauss(0, 10)
        altitude_effect = -self.location.get("altitude", 0) * 0.12  # hPa per meter
        
        pressure = self.base_pressure + weather_variation + altitude_effect
        return max(950, min(1050, pressure))  # Realistic pressure bounds
    
    def read(self) -> dict:
        """Override to provide multi-sensor data"""
        base_data = super().read()
        
        if "error" not in base_data:
            # Get readings from individual sensors
            temp_data = self.temp_sensor.read()
            humidity_data = self.humidity_sensor.read()
            
            # Combine into single reading
            base_data.update({
                "temperature": temp_data.get("value"),
                "temperature_unit": "°C",
                "humidity": humidity_data.get("value"),
                "humidity_unit": "%RH",
                "pressure": base_data["value"],
                "pressure_unit": "hPa",
                "sensor_type": "Environmental"
            })
            
            # Remove the generic value since we have specific readings
            base_data.pop("value", None)
            base_data.pop("unit", None)
        
        return base_data
