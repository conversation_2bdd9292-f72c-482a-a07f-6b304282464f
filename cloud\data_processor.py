"""
Cloud Data Processing Service for IoT Smart Irrigation System
Handles real-time data ingestion, processing, and storage with analytics
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from collections import deque
import statistics
import numpy as np
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


@dataclass
class SensorReading:
    """Structured sensor reading data"""
    sensor_id: str
    sensor_type: str
    timestamp: datetime
    value: float
    unit: str
    location: Dict[str, float]
    quality_score: float = 1.0
    metadata: Dict[str, Any] = None


@dataclass
class ProcessingRule:
    """Data processing rule configuration"""
    rule_id: str
    sensor_types: List[str]
    condition: str
    action: str
    parameters: Dict[str, Any]
    enabled: bool = True


class DataQualityAnalyzer:
    """Analyzes and scores data quality for IoT sensor readings"""
    
    def __init__(self):
        self.quality_thresholds = {
            "outlier_std_multiplier": 3.0,
            "min_signal_strength": -85,  # dBm
            "min_battery_level": 10.0,   # %
            "max_age_minutes": 60        # minutes
        }
    
    def analyze_reading(self, reading: SensorReading, historical_data: List[float]) -> float:
        """
        Analyze data quality and return quality score (0.0 to 1.0)
        
        Args:
            reading: Current sensor reading
            historical_data: Recent historical values for comparison
            
        Returns:
            Quality score between 0.0 (poor) and 1.0 (excellent)
        """
        quality_factors = []
        
        # Check for outliers using statistical analysis
        if historical_data and len(historical_data) >= 5:
            mean_val = statistics.mean(historical_data)
            std_val = statistics.stdev(historical_data)
            
            if std_val > 0:
                z_score = abs(reading.value - mean_val) / std_val
                outlier_factor = max(0, 1 - (z_score / self.quality_thresholds["outlier_std_multiplier"]))
                quality_factors.append(outlier_factor)
        
        # Check metadata quality indicators
        if reading.metadata:
            # Signal strength quality
            signal_strength = reading.metadata.get("signal_strength", -50)
            signal_factor = min(1.0, max(0.0, 
                (signal_strength - self.quality_thresholds["min_signal_strength"]) / 
                (-40 - self.quality_thresholds["min_signal_strength"])
            ))
            quality_factors.append(signal_factor)
            
            # Battery level quality
            battery_level = reading.metadata.get("battery_level", 100)
            battery_factor = min(1.0, max(0.0,
                (battery_level - self.quality_thresholds["min_battery_level"]) /
                (100 - self.quality_thresholds["min_battery_level"])
            ))
            quality_factors.append(battery_factor)
            
            # Data freshness
            age_minutes = (datetime.now() - reading.timestamp).total_seconds() / 60
            freshness_factor = max(0.0, 1 - (age_minutes / self.quality_thresholds["max_age_minutes"]))
            quality_factors.append(freshness_factor)
        
        # Return average quality score
        return statistics.mean(quality_factors) if quality_factors else 0.8


class StreamProcessor:
    """Real-time stream processing for IoT sensor data"""
    
    def __init__(self, window_size: int = 100):
        """
        Initialize stream processor
        
        Args:
            window_size: Size of sliding window for calculations
        """
        self.window_size = window_size
        self.data_windows: Dict[str, deque] = {}
        self.processing_rules: List[ProcessingRule] = []
        self.quality_analyzer = DataQualityAnalyzer()
        
        # Processing statistics
        self.stats = {
            "total_processed": 0,
            "quality_filtered": 0,
            "anomalies_detected": 0,
            "rules_triggered": 0
        }
    
    def add_processing_rule(self, rule: ProcessingRule):
        """Add data processing rule"""
        self.processing_rules.append(rule)
        logger.info(f"Added processing rule: {rule.rule_id}")
    
    def process_reading(self, reading: SensorReading) -> Dict[str, Any]:
        """
        Process individual sensor reading through the pipeline
        
        Args:
            reading: Sensor reading to process
            
        Returns:
            Processing result with enriched data and analysis
        """
        self.stats["total_processed"] += 1
        
        # Get historical data for this sensor
        sensor_key = f"{reading.sensor_id}_{reading.sensor_type}"
        if sensor_key not in self.data_windows:
            self.data_windows[sensor_key] = deque(maxlen=self.window_size)
        
        historical_values = list(self.data_windows[sensor_key])
        
        # Analyze data quality
        quality_score = self.quality_analyzer.analyze_reading(reading, historical_values)
        reading.quality_score = quality_score
        
        # Filter low quality data
        if quality_score < 0.3:
            self.stats["quality_filtered"] += 1
            return {
                "status": "filtered",
                "reason": "low_quality",
                "quality_score": quality_score,
                "reading": reading
            }
        
        # Add to sliding window
        self.data_windows[sensor_key].append(reading.value)
        
        # Perform statistical analysis
        analysis = self._perform_statistical_analysis(reading, historical_values)
        
        # Check processing rules
        rule_results = self._apply_processing_rules(reading, analysis)
        
        # Detect anomalies
        anomaly_result = self._detect_anomalies(reading, historical_values)
        if anomaly_result["is_anomaly"]:
            self.stats["anomalies_detected"] += 1
        
        return {
            "status": "processed",
            "reading": reading,
            "quality_score": quality_score,
            "analysis": analysis,
            "anomaly": anomaly_result,
            "rule_results": rule_results,
            "timestamp": datetime.now().isoformat()
        }
    
    def _perform_statistical_analysis(self, reading: SensorReading, historical_data: List[float]) -> Dict[str, Any]:
        """Perform statistical analysis on sensor data"""
        if not historical_data:
            return {"status": "insufficient_data"}
        
        try:
            analysis = {
                "current_value": reading.value,
                "mean": statistics.mean(historical_data),
                "median": statistics.median(historical_data),
                "std_dev": statistics.stdev(historical_data) if len(historical_data) > 1 else 0,
                "min": min(historical_data),
                "max": max(historical_data),
                "trend": self._calculate_trend(historical_data),
                "variance": statistics.variance(historical_data) if len(historical_data) > 1 else 0
            }
            
            # Calculate percentiles
            if len(historical_data) >= 4:
                sorted_data = sorted(historical_data)
                analysis["percentile_25"] = np.percentile(sorted_data, 25)
                analysis["percentile_75"] = np.percentile(sorted_data, 75)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in statistical analysis: {e}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_trend(self, data: List[float]) -> str:
        """Calculate trend direction from historical data"""
        if len(data) < 3:
            return "unknown"
        
        # Simple trend calculation using linear regression slope
        x = list(range(len(data)))
        n = len(data)
        
        sum_x = sum(x)
        sum_y = sum(data)
        sum_xy = sum(x[i] * data[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
        
        if slope > 0.1:
            return "increasing"
        elif slope < -0.1:
            return "decreasing"
        else:
            return "stable"
    
    def _detect_anomalies(self, reading: SensorReading, historical_data: List[float]) -> Dict[str, Any]:
        """Detect anomalies in sensor readings"""
        if len(historical_data) < 5:
            return {"is_anomaly": False, "reason": "insufficient_data"}
        
        try:
            mean_val = statistics.mean(historical_data)
            std_val = statistics.stdev(historical_data)
            
            if std_val == 0:
                return {"is_anomaly": False, "reason": "no_variation"}
            
            z_score = abs(reading.value - mean_val) / std_val
            
            # Anomaly thresholds
            if z_score > 3.0:
                return {
                    "is_anomaly": True,
                    "type": "statistical_outlier",
                    "z_score": z_score,
                    "severity": "high" if z_score > 4.0 else "medium"
                }
            
            # Check for sensor-specific anomalies
            sensor_anomalies = self._check_sensor_specific_anomalies(reading)
            if sensor_anomalies["is_anomaly"]:
                return sensor_anomalies
            
            return {"is_anomaly": False, "z_score": z_score}
            
        except Exception as e:
            logger.error(f"Error in anomaly detection: {e}")
            return {"is_anomaly": False, "error": str(e)}
    
    def _check_sensor_specific_anomalies(self, reading: SensorReading) -> Dict[str, Any]:
        """Check for sensor-type specific anomalies"""
        
        # Soil moisture specific checks
        if "moisture" in reading.sensor_type.lower():
            if reading.value < 0 or reading.value > 100:
                return {
                    "is_anomaly": True,
                    "type": "range_violation",
                    "reason": "moisture_out_of_range",
                    "severity": "high"
                }
        
        # Temperature specific checks
        elif "temperature" in reading.sensor_type.lower():
            if reading.value < -40 or reading.value > 60:
                return {
                    "is_anomaly": True,
                    "type": "range_violation",
                    "reason": "temperature_out_of_range",
                    "severity": "high"
                }
        
        # pH specific checks
        elif "ph" in reading.sensor_type.lower():
            if reading.value < 0 or reading.value > 14:
                return {
                    "is_anomaly": True,
                    "type": "range_violation",
                    "reason": "ph_out_of_range",
                    "severity": "high"
                }
        
        return {"is_anomaly": False}
    
    def _apply_processing_rules(self, reading: SensorReading, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Apply processing rules to sensor data"""
        results = []
        
        for rule in self.processing_rules:
            if not rule.enabled:
                continue
            
            if reading.sensor_type not in rule.sensor_types:
                continue
            
            try:
                # Simple rule evaluation (can be extended with more complex logic)
                if self._evaluate_rule_condition(rule, reading, analysis):
                    self.stats["rules_triggered"] += 1
                    
                    result = {
                        "rule_id": rule.rule_id,
                        "action": rule.action,
                        "parameters": rule.parameters,
                        "triggered_at": datetime.now().isoformat(),
                        "sensor_id": reading.sensor_id,
                        "trigger_value": reading.value
                    }
                    results.append(result)
                    
                    logger.info(f"Rule {rule.rule_id} triggered for sensor {reading.sensor_id}")
            
            except Exception as e:
                logger.error(f"Error evaluating rule {rule.rule_id}: {e}")
        
        return results
    
    def _evaluate_rule_condition(self, rule: ProcessingRule, reading: SensorReading, analysis: Dict[str, Any]) -> bool:
        """Evaluate rule condition (simplified implementation)"""
        
        # Example rule conditions
        if rule.condition == "low_moisture":
            return reading.value < rule.parameters.get("threshold", 30)
        elif rule.condition == "high_temperature":
            return reading.value > rule.parameters.get("threshold", 35)
        elif rule.condition == "anomaly_detected":
            return analysis.get("anomaly", {}).get("is_anomaly", False)
        elif rule.condition == "trend_decreasing":
            return analysis.get("trend") == "decreasing"
        
        return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return {
            "processing_stats": self.stats.copy(),
            "active_sensors": len(self.data_windows),
            "active_rules": len([r for r in self.processing_rules if r.enabled]),
            "window_size": self.window_size
        }
