"""
Data Analytics Engine for IoT Smart Irrigation System
Provides historical analysis, trend detection, and predictive insights
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import seaborn as sns
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
import logging
from dataclasses import dataclass
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


@dataclass
class AnalyticsReport:
    """Analytics report structure"""
    report_id: str
    report_type: str
    generated_at: datetime
    period_start: datetime
    period_end: datetime
    summary: Dict[str, Any]
    insights: List[str]
    recommendations: List[str]
    charts: Dict[str, str]  # Chart name -> file path or base64


class IrrigationAnalytics:
    """
    Comprehensive analytics engine for irrigation system data
    Provides insights for optimization and decision making
    """
    
    def __init__(self):
        """Initialize analytics engine"""
        self.data_cache = {}
        self.models = {}
        self.reports_generated = 0
        
        # Configure plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def generate_comprehensive_report(self, 
                                    sensor_data: List[Dict[str, Any]], 
                                    irrigation_events: List[Dict[str, Any]],
                                    period_days: int = 30) -> AnalyticsReport:
        """
        Generate comprehensive analytics report
        
        Args:
            sensor_data: Historical sensor readings
            irrigation_events: Historical irrigation events
            period_days: Analysis period in days
            
        Returns:
            Complete analytics report with insights and recommendations
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=period_days)
        
        # Convert to DataFrames for analysis
        df_sensors = self._prepare_sensor_dataframe(sensor_data, start_date, end_date)
        df_irrigation = self._prepare_irrigation_dataframe(irrigation_events, start_date, end_date)
        
        # Perform various analyses
        water_usage_analysis = self._analyze_water_usage(df_sensors, df_irrigation)
        efficiency_analysis = self._analyze_irrigation_efficiency(df_sensors, df_irrigation)
        trend_analysis = self._analyze_trends(df_sensors)
        anomaly_analysis = self._detect_anomalies(df_sensors)
        optimization_analysis = self._analyze_optimization_opportunities(df_sensors, df_irrigation)
        
        # Generate insights and recommendations
        insights = self._generate_insights(
            water_usage_analysis, efficiency_analysis, trend_analysis, anomaly_analysis
        )
        recommendations = self._generate_recommendations(
            water_usage_analysis, efficiency_analysis, optimization_analysis
        )
        
        # Create visualizations
        charts = self._create_report_charts(df_sensors, df_irrigation)
        
        # Compile summary
        summary = {
            "period_days": period_days,
            "total_sensor_readings": len(df_sensors),
            "total_irrigation_events": len(df_irrigation),
            "water_usage": water_usage_analysis,
            "efficiency": efficiency_analysis,
            "trends": trend_analysis,
            "anomalies": anomaly_analysis,
            "optimization": optimization_analysis
        }
        
        report = AnalyticsReport(
            report_id=f"report_{self.reports_generated}_{int(datetime.now().timestamp())}",
            report_type="comprehensive",
            generated_at=datetime.now(),
            period_start=start_date,
            period_end=end_date,
            summary=summary,
            insights=insights,
            recommendations=recommendations,
            charts=charts
        )
        
        self.reports_generated += 1
        logger.info(f"Generated comprehensive report: {report.report_id}")
        
        return report
    
    def _prepare_sensor_dataframe(self, sensor_data: List[Dict[str, Any]], 
                                 start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Prepare sensor data for analysis"""
        if not sensor_data:
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(sensor_data)
        
        # Parse timestamps
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Filter by date range
        df = df[(df['timestamp'] >= start_date) & (df['timestamp'] <= end_date)]
        
        # Add derived columns
        df['hour'] = df['timestamp'].dt.hour
        df['day_of_week'] = df['timestamp'].dt.dayofweek
        df['date'] = df['timestamp'].dt.date
        
        return df.sort_values('timestamp')
    
    def _prepare_irrigation_dataframe(self, irrigation_events: List[Dict[str, Any]], 
                                    start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Prepare irrigation events for analysis"""
        if not irrigation_events:
            return pd.DataFrame()
        
        df = pd.DataFrame(irrigation_events)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df[(df['timestamp'] >= start_date) & (df['timestamp'] <= end_date)]
        
        return df.sort_values('timestamp')
    
    def _analyze_water_usage(self, df_sensors: pd.DataFrame, df_irrigation: pd.DataFrame) -> Dict[str, Any]:
        """Analyze water usage patterns and efficiency"""
        if df_irrigation.empty:
            return {"total_usage": 0, "daily_average": 0, "efficiency_score": 0}
        
        # Calculate total water usage (assuming flow rate data)
        total_usage = 0
        daily_usage = {}
        
        for _, event in df_irrigation.iterrows():
            if event.get('action') == 'start' and 'duration_minutes' in event:
                # Assume 5 L/min flow rate
                water_used = event['duration_minutes'] * 5
                total_usage += water_used
                
                date = event['timestamp'].date()
                daily_usage[date] = daily_usage.get(date, 0) + water_used
        
        # Calculate efficiency metrics
        daily_average = total_usage / max(1, len(daily_usage))
        
        # Efficiency score based on moisture improvement vs water used
        efficiency_score = self._calculate_efficiency_score(df_sensors, df_irrigation)
        
        return {
            "total_usage_liters": total_usage,
            "daily_average_liters": daily_average,
            "irrigation_events": len(df_irrigation),
            "efficiency_score": efficiency_score,
            "daily_usage": daily_usage
        }
    
    def _calculate_efficiency_score(self, df_sensors: pd.DataFrame, df_irrigation: pd.DataFrame) -> float:
        """Calculate irrigation efficiency score (0-100)"""
        if df_sensors.empty or df_irrigation.empty:
            return 0.0
        
        # Simplified efficiency calculation
        # In a real system, this would be more sophisticated
        
        # Get moisture sensor data
        moisture_data = df_sensors[df_sensors['sensor_type'].str.contains('moisture', case=False, na=False)]
        
        if moisture_data.empty:
            return 50.0  # Default score
        
        # Calculate average moisture improvement after irrigation
        avg_moisture = moisture_data['value'].mean()
        moisture_variance = moisture_data['value'].var()
        
        # Higher average moisture and lower variance = better efficiency
        efficiency = min(100, max(0, (avg_moisture - 20) * 2 - moisture_variance * 0.5))
        
        return round(efficiency, 1)
    
    def _analyze_irrigation_efficiency(self, df_sensors: pd.DataFrame, df_irrigation: pd.DataFrame) -> Dict[str, Any]:
        """Analyze irrigation efficiency by zone and time"""
        efficiency_by_zone = {}
        efficiency_by_hour = {}
        
        # Group by zone
        if not df_irrigation.empty and 'zone_id' in df_irrigation.columns:
            for zone in df_irrigation['zone_id'].unique():
                zone_irrigation = df_irrigation[df_irrigation['zone_id'] == zone]
                zone_sensors = df_sensors[df_sensors['sensor_id'].str.contains(zone, case=False, na=False)]
                
                efficiency_by_zone[zone] = self._calculate_efficiency_score(zone_sensors, zone_irrigation)
        
        # Group by hour of day
        if not df_irrigation.empty:
            for hour in range(24):
                hour_irrigation = df_irrigation[df_irrigation['timestamp'].dt.hour == hour]
                hour_sensors = df_sensors[df_sensors['timestamp'].dt.hour == hour]
                
                if not hour_irrigation.empty:
                    efficiency_by_hour[hour] = self._calculate_efficiency_score(hour_sensors, hour_irrigation)
        
        return {
            "by_zone": efficiency_by_zone,
            "by_hour": efficiency_by_hour,
            "best_zone": max(efficiency_by_zone.items(), key=lambda x: x[1]) if efficiency_by_zone else None,
            "best_hour": max(efficiency_by_hour.items(), key=lambda x: x[1]) if efficiency_by_hour else None
        }
    
    def _analyze_trends(self, df_sensors: pd.DataFrame) -> Dict[str, Any]:
        """Analyze trends in sensor data"""
        if df_sensors.empty:
            return {}
        
        trends = {}
        
        # Analyze trends by sensor type
        for sensor_type in df_sensors['sensor_type'].unique():
            sensor_data = df_sensors[df_sensors['sensor_type'] == sensor_type]
            
            if len(sensor_data) < 10:  # Need minimum data points
                continue
            
            # Calculate trend using linear regression
            X = np.arange(len(sensor_data)).reshape(-1, 1)
            y = sensor_data['value'].values
            
            try:
                model = LinearRegression()
                model.fit(X, y)
                
                slope = model.coef_[0]
                r_squared = model.score(X, y)
                
                # Determine trend direction
                if abs(slope) < 0.01:
                    direction = "stable"
                elif slope > 0:
                    direction = "increasing"
                else:
                    direction = "decreasing"
                
                trends[sensor_type] = {
                    "direction": direction,
                    "slope": slope,
                    "strength": r_squared,
                    "confidence": "high" if r_squared > 0.7 else "medium" if r_squared > 0.4 else "low"
                }
                
            except Exception as e:
                logger.warning(f"Error calculating trend for {sensor_type}: {e}")
        
        return trends
    
    def _detect_anomalies(self, df_sensors: pd.DataFrame) -> Dict[str, Any]:
        """Detect anomalies in sensor data"""
        if df_sensors.empty:
            return {}
        
        anomalies = {}
        
        for sensor_type in df_sensors['sensor_type'].unique():
            sensor_data = df_sensors[df_sensors['sensor_type'] == sensor_type]
            
            if len(sensor_data) < 20:  # Need sufficient data
                continue
            
            values = sensor_data['value'].values
            
            # Statistical anomaly detection using IQR method
            Q1 = np.percentile(values, 25)
            Q3 = np.percentile(values, 75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            anomaly_indices = np.where((values < lower_bound) | (values > upper_bound))[0]
            
            if len(anomaly_indices) > 0:
                anomaly_data = sensor_data.iloc[anomaly_indices]
                
                anomalies[sensor_type] = {
                    "count": len(anomaly_indices),
                    "percentage": (len(anomaly_indices) / len(values)) * 100,
                    "timestamps": anomaly_data['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                    "values": anomaly_data['value'].tolist(),
                    "bounds": {"lower": lower_bound, "upper": upper_bound}
                }
        
        return anomalies
    
    def _analyze_optimization_opportunities(self, df_sensors: pd.DataFrame, df_irrigation: pd.DataFrame) -> Dict[str, Any]:
        """Identify optimization opportunities"""
        opportunities = []
        
        # Water usage optimization
        if not df_irrigation.empty:
            # Check for over-irrigation
            frequent_irrigation = df_irrigation.groupby('zone_id').size()
            for zone, count in frequent_irrigation.items():
                if count > 10:  # More than 10 irrigations in period
                    opportunities.append({
                        "type": "reduce_frequency",
                        "zone": zone,
                        "description": f"Zone {zone} irrigated {count} times - consider reducing frequency",
                        "potential_savings": f"{count * 0.1:.1f}% water reduction"
                    })
        
        # Timing optimization
        if not df_irrigation.empty:
            irrigation_hours = df_irrigation['timestamp'].dt.hour
            peak_hours = irrigation_hours.value_counts().head(3).index.tolist()
            
            if any(hour in [10, 11, 12, 13, 14, 15] for hour in peak_hours):
                opportunities.append({
                    "type": "timing_optimization",
                    "description": "Irrigation during peak evaporation hours detected",
                    "recommendation": "Schedule irrigation for early morning (5-7 AM) or evening (7-9 PM)",
                    "potential_savings": "15-25% water reduction"
                })
        
        # Sensor-based optimization
        if not df_sensors.empty:
            moisture_sensors = df_sensors[df_sensors['sensor_type'].str.contains('moisture', case=False, na=False)]
            
            if not moisture_sensors.empty:
                avg_moisture = moisture_sensors.groupby('sensor_id')['value'].mean()
                
                for sensor_id, avg_val in avg_moisture.items():
                    if avg_val > 70:
                        opportunities.append({
                            "type": "threshold_adjustment",
                            "sensor": sensor_id,
                            "description": f"Sensor {sensor_id} shows high average moisture ({avg_val:.1f}%)",
                            "recommendation": "Consider raising irrigation threshold",
                            "potential_savings": "10-15% water reduction"
                        })
        
        return {
            "opportunities": opportunities,
            "total_opportunities": len(opportunities),
            "estimated_total_savings": f"{len(opportunities) * 12:.0f}% potential water savings"
        }
    
    def _generate_insights(self, water_usage: Dict, efficiency: Dict, trends: Dict, anomalies: Dict) -> List[str]:
        """Generate actionable insights from analysis"""
        insights = []
        
        # Water usage insights
        if water_usage.get('efficiency_score', 0) > 80:
            insights.append("🌟 Excellent irrigation efficiency - system is well-optimized")
        elif water_usage.get('efficiency_score', 0) < 50:
            insights.append("⚠️ Low irrigation efficiency detected - optimization needed")
        
        # Efficiency insights
        if efficiency.get('best_zone'):
            best_zone, best_score = efficiency.get('best_zone')
            insights.append(f"🏆 Zone '{best_zone}' shows best efficiency ({best_score:.1f}%) - replicate settings")
        
        if efficiency.get('best_hour'):
            best_hour, _ = efficiency.get('best_hour')
            insights.append(f"⏰ Most efficient irrigation time: {best_hour}:00 - schedule more events then")
        
        # Trend insights
        for sensor_type, trend_data in trends.items():
            if trend_data['direction'] == 'decreasing' and 'moisture' in sensor_type.lower():
                insights.append(f"📉 Declining moisture trend in {sensor_type} - increase irrigation frequency")
            elif trend_data['direction'] == 'increasing' and 'temperature' in sensor_type.lower():
                insights.append(f"📈 Rising temperature trend - adjust irrigation schedule for higher evaporation")
        
        # Anomaly insights
        for sensor_type, anomaly_data in anomalies.items():
            if anomaly_data['percentage'] > 5:
                insights.append(f"🔍 {anomaly_data['percentage']:.1f}% anomalies in {sensor_type} - check sensor calibration")
        
        return insights
    
    def _generate_recommendations(self, water_usage: Dict, efficiency: Dict, optimization: Dict) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Water usage recommendations
        if water_usage.get('efficiency_score', 0) < 70:
            recommendations.append("Implement soil moisture-based irrigation scheduling")
            recommendations.append("Install drip irrigation for better water efficiency")
        
        # Timing recommendations
        recommendations.append("Schedule irrigation during early morning (5-7 AM) for minimal evaporation")
        recommendations.append("Avoid irrigation during peak sun hours (11 AM - 3 PM)")
        
        # Technology recommendations
        recommendations.append("Consider weather forecast integration for irrigation planning")
        recommendations.append("Implement variable rate irrigation based on soil type")
        
        # Optimization recommendations
        for opportunity in optimization.get('opportunities', []):
            recommendations.append(opportunity.get('recommendation', opportunity.get('description')))
        
        return recommendations
    
    def _create_report_charts(self, df_sensors: pd.DataFrame, df_irrigation: pd.DataFrame) -> Dict[str, str]:
        """Create visualization charts for the report"""
        charts = {}
        
        try:
            # 1. Moisture levels over time
            if not df_sensors.empty:
                moisture_data = df_sensors[df_sensors['sensor_type'].str.contains('moisture', case=False, na=False)]
                
                if not moisture_data.empty:
                    fig = px.line(moisture_data, x='timestamp', y='value', color='sensor_id',
                                title='Soil Moisture Levels Over Time',
                                labels={'value': 'Moisture (%)', 'timestamp': 'Time'})
                    charts['moisture_trends'] = fig.to_html()
            
            # 2. Irrigation events timeline
            if not df_irrigation.empty:
                irrigation_summary = df_irrigation.groupby(['zone_id', df_irrigation['timestamp'].dt.date]).size().reset_index()
                irrigation_summary.columns = ['zone_id', 'date', 'events']
                
                fig = px.bar(irrigation_summary, x='date', y='events', color='zone_id',
                           title='Daily Irrigation Events by Zone',
                           labels={'events': 'Number of Events', 'date': 'Date'})
                charts['irrigation_timeline'] = fig.to_html()
            
            # 3. Water usage by zone
            if not df_irrigation.empty and 'zone_id' in df_irrigation.columns:
                zone_usage = df_irrigation.groupby('zone_id').agg({
                    'duration_minutes': 'sum'
                }).reset_index()
                zone_usage['water_liters'] = zone_usage['duration_minutes'] * 5  # 5 L/min
                
                fig = px.pie(zone_usage, values='water_liters', names='zone_id',
                           title='Water Usage Distribution by Zone')
                charts['water_usage_distribution'] = fig.to_html()
            
        except Exception as e:
            logger.error(f"Error creating charts: {e}")
        
        return charts
    
    def export_report_to_html(self, report: AnalyticsReport, filename: str = None) -> str:
        """Export analytics report to HTML file"""
        if not filename:
            filename = f"irrigation_report_{report.report_id}.html"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>IoT Irrigation Analytics Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 8px; }}
                .section {{ margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 15px; background: white; border-radius: 4px; }}
                .insight {{ background: #e8f5e8; padding: 10px; margin: 5px 0; border-left: 4px solid #27ae60; }}
                .recommendation {{ background: #fff3cd; padding: 10px; margin: 5px 0; border-left: 4px solid #ffc107; }}
                .chart {{ margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🌱 IoT Smart Irrigation Analytics Report</h1>
                <p>Report ID: {report.report_id}</p>
                <p>Generated: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Period: {report.period_start.strftime('%Y-%m-%d')} to {report.period_end.strftime('%Y-%m-%d')}</p>
            </div>
            
            <div class="section">
                <h2>📊 Summary Metrics</h2>
                <div class="metric">
                    <h3>Water Usage</h3>
                    <p>{report.summary.get('water_usage', {}).get('total_usage_liters', 0):.1f} L</p>
                </div>
                <div class="metric">
                    <h3>Efficiency Score</h3>
                    <p>{report.summary.get('water_usage', {}).get('efficiency_score', 0):.1f}%</p>
                </div>
                <div class="metric">
                    <h3>Irrigation Events</h3>
                    <p>{report.summary.get('water_usage', {}).get('irrigation_events', 0)}</p>
                </div>
            </div>
            
            <div class="section">
                <h2>💡 Key Insights</h2>
                {''.join(f'<div class="insight">{insight}</div>' for insight in report.insights)}
            </div>
            
            <div class="section">
                <h2>🎯 Recommendations</h2>
                {''.join(f'<div class="recommendation">{rec}</div>' for rec in report.recommendations)}
            </div>
            
            <div class="section">
                <h2>📈 Visualizations</h2>
                {''.join(f'<div class="chart">{chart}</div>' for chart in report.charts.values())}
            </div>
        </body>
        </html>
        """
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"Report exported to {filename}")
        return filename
