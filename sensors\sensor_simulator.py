"""
IoT Sensor Network Simulator
Orchestrates multiple sensors to simulate a real IoT irrigation field deployment
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor

from .soil_moisture_sensor import SoilMoistureSensor
from .environmental_sensors import (
    TemperatureSensor, HumiditySensor, LightSensor, 
    WaterFlowSensor, PHSensor, CombinedEnvironmentalSensor
)

logger = logging.getLogger(__name__)


class SensorNetwork:
    """
    Manages a network of IoT sensors simulating a real irrigation field
    Demonstrates realistic IoT deployment patterns and data collection
    """
    
    def __init__(self, field_config: Dict[str, Any]):
        """
        Initialize sensor network based on field configuration
        
        Args:
            field_config: Configuration defining field layout and sensors
        """
        self.field_config = field_config
        self.sensors: Dict[str, Any] = {}
        self.is_running = False
        self.data_callback = None
        self.reading_interval = 30  # seconds
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # Network statistics
        self.total_readings = 0
        self.failed_readings = 0
        self.network_uptime = datetime.now()
        
        self._setup_sensor_network()
    
    def _setup_sensor_network(self):
        """Setup sensors based on field configuration"""
        field_name = self.field_config.get("name", "default_field")
        zones = self.field_config.get("zones", [])
        
        logger.info(f"Setting up sensor network for field: {field_name}")
        
        for zone_idx, zone in enumerate(zones):
            zone_name = zone.get("name", f"zone_{zone_idx}")
            zone_location = zone.get("location", {"lat": 40.7128, "lng": -74.0060})
            soil_type = zone.get("soil_type", "loam")
            
            # Create sensors for each zone
            self._create_zone_sensors(zone_name, zone_location, soil_type, zone_idx)
        
        # Create field-level sensors (weather station)
        self._create_field_sensors()
        
        logger.info(f"Sensor network initialized with {len(self.sensors)} sensors")
    
    def _create_zone_sensors(self, zone_name: str, location: dict, soil_type: str, zone_idx: int):
        """Create sensors for a specific irrigation zone"""
        
        # Soil moisture sensors at different depths
        for depth in [10, 20, 30]:  # cm
            sensor_id = f"{zone_name}_moisture_{depth}cm"
            self.sensors[sensor_id] = SoilMoistureSensor(
                sensor_id=sensor_id,
                location=location,
                soil_type=soil_type,
                depth=depth,
                noise_level=0.02
            )
        
        # pH sensor for soil analysis
        ph_sensor_id = f"{zone_name}_ph"
        self.sensors[ph_sensor_id] = PHSensor(
            sensor_id=ph_sensor_id,
            location=location,
            soil_type=soil_type
        )
        
        # Water flow sensor for irrigation monitoring
        flow_sensor_id = f"{zone_name}_flow"
        self.sensors[flow_sensor_id] = WaterFlowSensor(
            sensor_id=flow_sensor_id,
            location=location
        )
        
        # Zone-specific environmental sensor
        env_sensor_id = f"{zone_name}_environment"
        self.sensors[env_sensor_id] = CombinedEnvironmentalSensor(
            sensor_id=env_sensor_id,
            location=location
        )
    
    def _create_field_sensors(self):
        """Create field-level sensors (weather station)"""
        field_location = self.field_config.get("center_location", {"lat": 40.7128, "lng": -74.0060})
        
        # Weather station sensors
        self.sensors["weather_temperature"] = TemperatureSensor(
            sensor_id="weather_temperature",
            location=field_location
        )
        
        self.sensors["weather_humidity"] = HumiditySensor(
            sensor_id="weather_humidity",
            location=field_location
        )
        
        self.sensors["weather_light"] = LightSensor(
            sensor_id="weather_light",
            location=field_location
        )
    
    def set_data_callback(self, callback):
        """Set callback function for sensor data"""
        self.data_callback = callback
    
    def set_reading_interval(self, interval_seconds: int):
        """Set sensor reading interval"""
        self.reading_interval = max(5, interval_seconds)  # Minimum 5 seconds
    
    async def start_monitoring(self):
        """Start continuous sensor monitoring"""
        if self.is_running:
            logger.warning("Sensor monitoring already running")
            return
        
        self.is_running = True
        self.network_uptime = datetime.now()
        logger.info("Starting sensor network monitoring")
        
        try:
            while self.is_running:
                await self._collect_sensor_data()
                await asyncio.sleep(self.reading_interval)
        except Exception as e:
            logger.error(f"Error in sensor monitoring: {e}")
        finally:
            self.is_running = False
    
    async def _collect_sensor_data(self):
        """Collect data from all sensors concurrently"""
        start_time = time.time()
        
        # Create tasks for concurrent sensor reading
        tasks = []
        for sensor_id, sensor in self.sensors.items():
            task = asyncio.create_task(self._read_sensor_async(sensor_id, sensor))
            tasks.append(task)
        
        # Wait for all sensor readings
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        sensor_data = {}
        for i, (sensor_id, result) in enumerate(zip(self.sensors.keys(), results)):
            if isinstance(result, Exception):
                logger.error(f"Error reading sensor {sensor_id}: {result}")
                self.failed_readings += 1
            else:
                sensor_data[sensor_id] = result
                self.total_readings += 1
        
        # Create network data package
        network_data = {
            "timestamp": datetime.now().isoformat(),
            "field_name": self.field_config.get("name", "default_field"),
            "collection_time_ms": round((time.time() - start_time) * 1000, 2),
            "sensors": sensor_data,
            "network_stats": self.get_network_stats()
        }
        
        # Send to callback if configured
        if self.data_callback:
            try:
                await self.data_callback(network_data)
            except Exception as e:
                logger.error(f"Error in data callback: {e}")
    
    async def _read_sensor_async(self, sensor_id: str, sensor):
        """Read sensor data asynchronously"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, sensor.read)
    
    def stop_monitoring(self):
        """Stop sensor monitoring"""
        self.is_running = False
        logger.info("Stopping sensor network monitoring")
    
    def get_network_stats(self) -> Dict[str, Any]:
        """Get network statistics"""
        uptime = datetime.now() - self.network_uptime
        success_rate = (self.total_readings / max(1, self.total_readings + self.failed_readings)) * 100
        
        return {
            "total_sensors": len(self.sensors),
            "active_sensors": len([s for s in self.sensors.values() if s.is_active]),
            "total_readings": self.total_readings,
            "failed_readings": self.failed_readings,
            "success_rate": round(success_rate, 2),
            "uptime_hours": round(uptime.total_seconds() / 3600, 2),
            "avg_battery_level": round(
                sum(s.battery_level for s in self.sensors.values()) / len(self.sensors), 1
            )
        }
    
    def get_sensor_status(self) -> Dict[str, Any]:
        """Get status of all sensors"""
        return {
            sensor_id: sensor.get_status() 
            for sensor_id, sensor in self.sensors.items()
        }
    
    def simulate_irrigation(self, zone_name: str, duration_minutes: float = 30):
        """Simulate irrigation event for a specific zone"""
        logger.info(f"Simulating irrigation for zone {zone_name} for {duration_minutes} minutes")
        
        # Activate flow sensor
        flow_sensor_id = f"{zone_name}_flow"
        if flow_sensor_id in self.sensors:
            self.sensors[flow_sensor_id].set_flow_state(True, 5.0)  # 5 L/min
        
        # Update soil moisture sensors
        for sensor_id, sensor in self.sensors.items():
            if sensor_id.startswith(zone_name) and "moisture" in sensor_id:
                sensor.simulate_irrigation(duration_minutes)
        
        # Schedule irrigation stop
        asyncio.create_task(self._stop_irrigation_after_delay(zone_name, duration_minutes))
    
    async def _stop_irrigation_after_delay(self, zone_name: str, duration_minutes: float):
        """Stop irrigation after specified duration"""
        await asyncio.sleep(duration_minutes * 60)  # Convert to seconds
        
        flow_sensor_id = f"{zone_name}_flow"
        if flow_sensor_id in self.sensors:
            self.sensors[flow_sensor_id].set_flow_state(False)
        
        logger.info(f"Irrigation stopped for zone {zone_name}")
    
    def calibrate_sensor(self, sensor_id: str, reference_value: float, measured_value: float):
        """Calibrate a specific sensor"""
        if sensor_id in self.sensors:
            self.sensors[sensor_id].calibrate(reference_value, measured_value)
            logger.info(f"Calibrated sensor {sensor_id}")
        else:
            logger.error(f"Sensor {sensor_id} not found")
    
    def set_sensor_active(self, sensor_id: str, active: bool):
        """Enable/disable a specific sensor"""
        if sensor_id in self.sensors:
            self.sensors[sensor_id].set_active(active)
        else:
            logger.error(f"Sensor {sensor_id} not found")
    
    def get_zone_summary(self, zone_name: str) -> Dict[str, Any]:
        """Get summary data for a specific zone"""
        zone_sensors = {
            sid: sensor for sid, sensor in self.sensors.items() 
            if sid.startswith(zone_name)
        }
        
        if not zone_sensors:
            return {"error": f"No sensors found for zone {zone_name}"}
        
        # Collect latest readings
        zone_data = {}
        for sensor_id, sensor in zone_sensors.items():
            try:
                reading = sensor.read()
                zone_data[sensor_id] = reading
            except Exception as e:
                logger.error(f"Error reading sensor {sensor_id}: {e}")
        
        return {
            "zone_name": zone_name,
            "timestamp": datetime.now().isoformat(),
            "sensor_count": len(zone_sensors),
            "sensors": zone_data
        }
