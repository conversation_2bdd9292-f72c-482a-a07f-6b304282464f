"""
Alert and Notification System for IoT Smart Irrigation
Handles critical alerts, notifications, and escalation procedures
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests

logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertType(Enum):
    """Types of alerts in the irrigation system"""
    SENSOR_FAILURE = "sensor_failure"
    LOW_MOISTURE = "low_moisture"
    HIGH_TEMPERATURE = "high_temperature"
    WATER_SHORTAGE = "water_shortage"
    PUMP_FAILURE = "pump_failure"
    VALVE_MALFUNCTION = "valve_malfunction"
    COMMUNICATION_LOSS = "communication_loss"
    POWER_OUTAGE = "power_outage"
    WEATHER_WARNING = "weather_warning"
    SYSTEM_ERROR = "system_error"
    MAINTENANCE_DUE = "maintenance_due"


class NotificationChannel(Enum):
    """Available notification channels"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    WEBHOOK = "webhook"
    DASHBOARD = "dashboard"
    MQTT = "mqtt"


@dataclass
class Alert:
    """Alert data structure"""
    alert_id: str
    alert_type: AlertType
    severity: AlertSeverity
    title: str
    message: str
    source: str  # sensor_id, zone_id, or system component
    timestamp: datetime
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['alert_type'] = self.alert_type.value
        data['severity'] = self.severity.value
        if self.acknowledged_at:
            data['acknowledged_at'] = self.acknowledged_at.isoformat()
        if self.resolved_at:
            data['resolved_at'] = self.resolved_at.isoformat()
        return data


@dataclass
class NotificationRule:
    """Notification rule configuration"""
    rule_id: str
    name: str
    alert_types: List[AlertType]
    severity_threshold: AlertSeverity
    channels: List[NotificationChannel]
    recipients: List[str]
    enabled: bool = True
    quiet_hours: Optional[Dict[str, int]] = None  # {"start": 22, "end": 6}
    escalation_delay_minutes: int = 30
    max_notifications_per_hour: int = 10


class EmailNotifier:
    """Email notification handler"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
    
    async def send_notification(self, alert: Alert, recipients: List[str]) -> bool:
        """Send email notification"""
        try:
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = f"🚨 Irrigation Alert: {alert.title}"
            
            # Create HTML body
            html_body = f"""
            <html>
            <body>
                <h2 style="color: {'#e74c3c' if alert.severity in [AlertSeverity.HIGH, AlertSeverity.CRITICAL] else '#f39c12'};">
                    🚨 Irrigation System Alert
                </h2>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3>{alert.title}</h3>
                    <p><strong>Severity:</strong> {alert.severity.value.upper()}</p>
                    <p><strong>Type:</strong> {alert.alert_type.value.replace('_', ' ').title()}</p>
                    <p><strong>Source:</strong> {alert.source}</p>
                    <p><strong>Time:</strong> {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div style="background: #fff; padding: 20px; border-left: 4px solid #3498db;">
                    <h4>Details:</h4>
                    <p>{alert.message}</p>
                </div>
                
                {self._get_action_recommendations(alert)}
                
                <hr>
                <p style="color: #7f8c8d; font-size: 12px;">
                    This is an automated message from the IoT Smart Irrigation System.
                    Alert ID: {alert.alert_id}
                </p>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(html_body, 'html'))
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"Email notification sent for alert {alert.alert_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return False
    
    def _get_action_recommendations(self, alert: Alert) -> str:
        """Get action recommendations based on alert type"""
        recommendations = {
            AlertType.SENSOR_FAILURE: "Check sensor connections and calibration. Replace if necessary.",
            AlertType.LOW_MOISTURE: "Verify irrigation schedule and soil conditions. Consider manual irrigation.",
            AlertType.HIGH_TEMPERATURE: "Increase irrigation frequency and check for adequate shade.",
            AlertType.WATER_SHORTAGE: "Check water supply and refill tanks. Verify pump operation.",
            AlertType.PUMP_FAILURE: "Inspect pump for mechanical issues. Check power supply and connections.",
            AlertType.VALVE_MALFUNCTION: "Test valve operation manually. Check for blockages or electrical issues.",
            AlertType.COMMUNICATION_LOSS: "Verify network connectivity and MQTT broker status.",
            AlertType.POWER_OUTAGE: "Check backup power systems and restore main power supply.",
            AlertType.WEATHER_WARNING: "Adjust irrigation schedule based on weather forecast.",
            AlertType.SYSTEM_ERROR: "Review system logs and restart affected components.",
            AlertType.MAINTENANCE_DUE: "Schedule maintenance according to system recommendations."
        }
        
        recommendation = recommendations.get(alert.alert_type, "Contact system administrator for assistance.")
        
        return f"""
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="color: #27ae60;">🔧 Recommended Actions:</h4>
            <p>{recommendation}</p>
        </div>
        """


class WebhookNotifier:
    """Webhook notification handler"""
    
    def __init__(self, webhook_urls: List[str]):
        self.webhook_urls = webhook_urls
    
    async def send_notification(self, alert: Alert, webhook_url: str) -> bool:
        """Send webhook notification"""
        try:
            payload = {
                "alert": alert.to_dict(),
                "timestamp": datetime.now().isoformat(),
                "system": "IoT Smart Irrigation"
            }
            
            response = requests.post(
                webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Webhook notification sent for alert {alert.alert_id}")
                return True
            else:
                logger.warning(f"Webhook returned status {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")
            return False


class NotificationSystem:
    """Main notification system coordinator"""
    
    def __init__(self):
        """Initialize notification system"""
        self.alerts: Dict[str, Alert] = {}
        self.notification_rules: List[NotificationRule] = []
        self.notifiers: Dict[NotificationChannel, Any] = {}
        
        # Notification statistics
        self.stats = {
            "total_alerts": 0,
            "alerts_by_severity": {severity.value: 0 for severity in AlertSeverity},
            "alerts_by_type": {alert_type.value: 0 for alert_type in AlertType},
            "notifications_sent": 0,
            "notification_failures": 0
        }
        
        # Rate limiting
        self.notification_history: List[datetime] = []
        self.max_notifications_per_minute = 10
        
        # Setup default rules
        self._setup_default_rules()
    
    def add_notifier(self, channel: NotificationChannel, notifier: Any):
        """Add notification channel handler"""
        self.notifiers[channel] = notifier
        logger.info(f"Added notifier for channel: {channel.value}")
    
    def add_notification_rule(self, rule: NotificationRule):
        """Add notification rule"""
        self.notification_rules.append(rule)
        logger.info(f"Added notification rule: {rule.name}")
    
    async def create_alert(self, 
                          alert_type: AlertType,
                          severity: AlertSeverity,
                          title: str,
                          message: str,
                          source: str,
                          metadata: Dict[str, Any] = None) -> str:
        """Create and process new alert"""
        
        # Generate alert ID
        alert_id = f"alert_{int(datetime.now().timestamp())}_{len(self.alerts)}"
        
        # Create alert
        alert = Alert(
            alert_id=alert_id,
            alert_type=alert_type,
            severity=severity,
            title=title,
            message=message,
            source=source,
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        
        # Store alert
        self.alerts[alert_id] = alert
        
        # Update statistics
        self.stats["total_alerts"] += 1
        self.stats["alerts_by_severity"][severity.value] += 1
        self.stats["alerts_by_type"][alert_type.value] += 1
        
        logger.info(f"Created alert {alert_id}: {title}")
        
        # Process notifications
        await self._process_alert_notifications(alert)
        
        return alert_id
    
    async def _process_alert_notifications(self, alert: Alert):
        """Process notifications for an alert"""
        
        # Check rate limiting
        if not self._check_rate_limit():
            logger.warning(f"Rate limit exceeded, skipping notifications for alert {alert.alert_id}")
            return
        
        # Find matching notification rules
        matching_rules = self._find_matching_rules(alert)
        
        if not matching_rules:
            logger.debug(f"No matching notification rules for alert {alert.alert_id}")
            return
        
        # Send notifications for each matching rule
        for rule in matching_rules:
            if not rule.enabled:
                continue
            
            # Check quiet hours
            if self._is_quiet_hours(rule):
                logger.debug(f"Skipping notifications during quiet hours for rule {rule.name}")
                continue
            
            # Send notifications through each channel
            for channel in rule.channels:
                if channel in self.notifiers:
                    try:
                        success = await self._send_notification(alert, channel, rule.recipients)
                        if success:
                            self.stats["notifications_sent"] += 1
                        else:
                            self.stats["notification_failures"] += 1
                    except Exception as e:
                        logger.error(f"Error sending notification via {channel.value}: {e}")
                        self.stats["notification_failures"] += 1
    
    def _find_matching_rules(self, alert: Alert) -> List[NotificationRule]:
        """Find notification rules that match the alert"""
        matching_rules = []
        
        for rule in self.notification_rules:
            # Check alert type
            if alert.alert_type not in rule.alert_types:
                continue
            
            # Check severity threshold
            severity_levels = [AlertSeverity.LOW, AlertSeverity.MEDIUM, AlertSeverity.HIGH, AlertSeverity.CRITICAL]
            alert_severity_index = severity_levels.index(alert.severity)
            threshold_index = severity_levels.index(rule.severity_threshold)
            
            if alert_severity_index < threshold_index:
                continue
            
            matching_rules.append(rule)
        
        return matching_rules
    
    def _check_rate_limit(self) -> bool:
        """Check if rate limit allows sending notification"""
        now = datetime.now()
        
        # Remove old entries (older than 1 minute)
        self.notification_history = [
            timestamp for timestamp in self.notification_history
            if (now - timestamp).total_seconds() < 60
        ]
        
        # Check if under limit
        if len(self.notification_history) >= self.max_notifications_per_minute:
            return False
        
        # Add current timestamp
        self.notification_history.append(now)
        return True
    
    def _is_quiet_hours(self, rule: NotificationRule) -> bool:
        """Check if current time is within quiet hours"""
        if not rule.quiet_hours:
            return False
        
        current_hour = datetime.now().hour
        start_hour = rule.quiet_hours.get("start", 22)
        end_hour = rule.quiet_hours.get("end", 6)
        
        if start_hour <= end_hour:
            return start_hour <= current_hour <= end_hour
        else:  # Quiet hours span midnight
            return current_hour >= start_hour or current_hour <= end_hour
    
    async def _send_notification(self, alert: Alert, channel: NotificationChannel, recipients: List[str]) -> bool:
        """Send notification through specific channel"""
        notifier = self.notifiers.get(channel)
        
        if not notifier:
            logger.warning(f"No notifier configured for channel {channel.value}")
            return False
        
        try:
            if channel == NotificationChannel.EMAIL:
                return await notifier.send_notification(alert, recipients)
            elif channel == NotificationChannel.WEBHOOK:
                # For webhooks, recipients are webhook URLs
                for webhook_url in recipients:
                    await notifier.send_notification(alert, webhook_url)
                return True
            else:
                logger.warning(f"Notification channel {channel.value} not implemented")
                return False
                
        except Exception as e:
            logger.error(f"Error in {channel.value} notifier: {e}")
            return False
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """Acknowledge an alert"""
        if alert_id not in self.alerts:
            return False
        
        alert = self.alerts[alert_id]
        alert.acknowledged = True
        alert.acknowledged_by = acknowledged_by
        alert.acknowledged_at = datetime.now()
        
        logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
        return True
    
    def resolve_alert(self, alert_id: str) -> bool:
        """Mark alert as resolved"""
        if alert_id not in self.alerts:
            return False
        
        alert = self.alerts[alert_id]
        alert.resolved = True
        alert.resolved_at = datetime.now()
        
        logger.info(f"Alert {alert_id} resolved")
        return True
    
    def get_active_alerts(self, severity_filter: AlertSeverity = None) -> List[Alert]:
        """Get list of active (unresolved) alerts"""
        active_alerts = [
            alert for alert in self.alerts.values()
            if not alert.resolved
        ]
        
        if severity_filter:
            active_alerts = [
                alert for alert in active_alerts
                if alert.severity == severity_filter
            ]
        
        return sorted(active_alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert and notification statistics"""
        active_alerts = self.get_active_alerts()
        
        return {
            "total_alerts": self.stats["total_alerts"],
            "active_alerts": len(active_alerts),
            "alerts_by_severity": self.stats["alerts_by_severity"],
            "alerts_by_type": self.stats["alerts_by_type"],
            "notifications_sent": self.stats["notifications_sent"],
            "notification_failures": self.stats["notification_failures"],
            "notification_rules": len(self.notification_rules),
            "configured_channels": list(self.notifiers.keys())
        }
    
    def _setup_default_rules(self):
        """Setup default notification rules"""
        
        # Critical alerts rule
        critical_rule = NotificationRule(
            rule_id="critical_alerts",
            name="Critical System Alerts",
            alert_types=list(AlertType),
            severity_threshold=AlertSeverity.CRITICAL,
            channels=[NotificationChannel.EMAIL, NotificationChannel.DASHBOARD],
            recipients=["<EMAIL>"],
            escalation_delay_minutes=5,
            max_notifications_per_hour=20
        )
        
        # High priority alerts rule
        high_priority_rule = NotificationRule(
            rule_id="high_priority_alerts",
            name="High Priority Alerts",
            alert_types=[
                AlertType.SENSOR_FAILURE,
                AlertType.PUMP_FAILURE,
                AlertType.VALVE_MALFUNCTION,
                AlertType.WATER_SHORTAGE
            ],
            severity_threshold=AlertSeverity.HIGH,
            channels=[NotificationChannel.EMAIL, NotificationChannel.DASHBOARD],
            recipients=["<EMAIL>"],
            quiet_hours={"start": 22, "end": 6},
            escalation_delay_minutes=15
        )
        
        # Maintenance alerts rule
        maintenance_rule = NotificationRule(
            rule_id="maintenance_alerts",
            name="Maintenance and Low Priority",
            alert_types=[AlertType.MAINTENANCE_DUE, AlertType.LOW_MOISTURE],
            severity_threshold=AlertSeverity.MEDIUM,
            channels=[NotificationChannel.DASHBOARD],
            recipients=["<EMAIL>"],
            quiet_hours={"start": 20, "end": 8},
            max_notifications_per_hour=5
        )
        
        self.notification_rules.extend([critical_rule, high_priority_rule, maintenance_rule])
        logger.info("Default notification rules configured")
