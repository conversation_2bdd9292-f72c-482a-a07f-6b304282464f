"""
Base sensor class for IoT Smart Irrigation System
Demonstrates real-world IoT sensor engineering principles
"""

import time
import random
import math
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class BaseSensor(ABC):
    """
    Abstract base class for all sensors in the IoT irrigation system.
    Implements common sensor functionality and patterns used in real IoT devices.
    """
    
    def __init__(self, 
                 sensor_id: str, 
                 location: Dict[str, float],
                 calibration_offset: float = 0.0,
                 noise_level: float = 0.02):
        """
        Initialize base sensor with common properties
        
        Args:
            sensor_id: Unique identifier for the sensor
            location: GPS coordinates {"lat": float, "lng": float}
            calibration_offset: Calibration offset for sensor accuracy
            noise_level: Noise level for realistic sensor simulation (0.0-1.0)
        """
        self.sensor_id = sensor_id
        self.location = location
        self.calibration_offset = calibration_offset
        self.noise_level = noise_level
        self.is_active = True
        self.last_reading_time = None
        self.battery_level = 100.0
        self.signal_strength = -50  # dBm
        self.error_count = 0
        self.total_readings = 0
        
        # Sensor health simulation
        self.health_status = "healthy"
        self.maintenance_due = False
        
    @abstractmethod
    def read_raw_value(self) -> float:
        """
        Read raw sensor value - to be implemented by specific sensors
        Returns: Raw sensor reading as float
        """
        pass
    
    @abstractmethod
    def get_unit(self) -> str:
        """
        Get the unit of measurement for this sensor
        Returns: Unit string (e.g., "°C", "%", "lux")
        """
        pass
    
    def add_noise(self, value: float) -> float:
        """
        Add realistic noise to sensor readings
        Simulates real-world sensor imperfections
        """
        if self.noise_level > 0:
            noise = random.gauss(0, self.noise_level * abs(value))
            return value + noise
        return value
    
    def apply_calibration(self, value: float) -> float:
        """
        Apply calibration offset to sensor reading
        Simulates sensor calibration in real devices
        """
        return value + self.calibration_offset
    
    def simulate_battery_drain(self):
        """
        Simulate battery drain over time
        Real IoT devices need power management
        """
        if self.battery_level > 0:
            # Drain battery based on usage (0.01% per reading)
            drain = 0.01 + random.uniform(0, 0.005)
            self.battery_level = max(0, self.battery_level - drain)
    
    def simulate_signal_strength(self):
        """
        Simulate varying signal strength
        Real IoT devices experience signal fluctuations
        """
        # Signal strength varies between -40 to -90 dBm
        base_strength = -50
        variation = random.gauss(0, 10)
        self.signal_strength = max(-90, min(-40, base_strength + variation))
    
    def check_sensor_health(self):
        """
        Simulate sensor health monitoring
        Real IoT systems need health diagnostics
        """
        # Simulate occasional sensor issues
        if random.random() < 0.001:  # 0.1% chance of error
            self.error_count += 1
            if self.error_count > 5:
                self.health_status = "degraded"
        
        # Simulate maintenance requirements
        if self.total_readings > 10000:
            self.maintenance_due = True
    
    def read(self) -> Dict[str, Any]:
        """
        Main method to read sensor data with all processing
        Returns complete sensor data package
        """
        try:
            if not self.is_active:
                raise Exception("Sensor is inactive")
            
            # Read raw value
            raw_value = self.read_raw_value()
            
            # Apply processing pipeline
            calibrated_value = self.apply_calibration(raw_value)
            final_value = self.add_noise(calibrated_value)
            
            # Update sensor state
            self.last_reading_time = datetime.now(timezone.utc)
            self.total_readings += 1
            self.simulate_battery_drain()
            self.simulate_signal_strength()
            self.check_sensor_health()
            
            # Return complete sensor data package
            return {
                "sensor_id": self.sensor_id,
                "sensor_type": self.__class__.__name__,
                "timestamp": self.last_reading_time.isoformat(),
                "location": self.location,
                "value": round(final_value, 2),
                "unit": self.get_unit(),
                "raw_value": round(raw_value, 2),
                "battery_level": round(self.battery_level, 1),
                "signal_strength": self.signal_strength,
                "health_status": self.health_status,
                "maintenance_due": self.maintenance_due,
                "error_count": self.error_count,
                "total_readings": self.total_readings
            }
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Sensor {self.sensor_id} error: {str(e)}")
            return {
                "sensor_id": self.sensor_id,
                "sensor_type": self.__class__.__name__,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e),
                "error_count": self.error_count
            }
    
    def calibrate(self, reference_value: float, measured_value: float):
        """
        Calibrate sensor based on reference measurement
        Real IoT sensors need periodic calibration
        """
        self.calibration_offset = reference_value - measured_value
        logger.info(f"Sensor {self.sensor_id} calibrated with offset: {self.calibration_offset}")
    
    def reset_error_count(self):
        """Reset error counter after maintenance"""
        self.error_count = 0
        self.health_status = "healthy"
        self.maintenance_due = False
        logger.info(f"Sensor {self.sensor_id} maintenance completed")
    
    def set_active(self, active: bool):
        """Enable/disable sensor"""
        self.is_active = active
        status = "activated" if active else "deactivated"
        logger.info(f"Sensor {self.sensor_id} {status}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive sensor status"""
        return {
            "sensor_id": self.sensor_id,
            "is_active": self.is_active,
            "battery_level": self.battery_level,
            "signal_strength": self.signal_strength,
            "health_status": self.health_status,
            "maintenance_due": self.maintenance_due,
            "error_count": self.error_count,
            "total_readings": self.total_readings,
            "last_reading": self.last_reading_time.isoformat() if self.last_reading_time else None
        }
