"""
System Configuration Management for IoT Smart Irrigation
Handles configuration, device management, and user access control
"""

import json
import yaml
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import secrets
from enum import Enum

logger = logging.getLogger(__name__)


class UserRole(Enum):
    """User roles for access control"""
    ADMIN = "admin"
    OPERATOR = "operator"
    VIEWER = "viewer"
    MAINTENANCE = "maintenance"


class DeviceType(Enum):
    """Device types in the system"""
    SENSOR = "sensor"
    VALVE = "valve"
    PUMP = "pump"
    CONTROLLER = "controller"
    GATEWAY = "gateway"


@dataclass
class User:
    """User account configuration"""
    user_id: str
    username: str
    email: str
    role: UserRole
    password_hash: str
    created_at: datetime
    last_login: Optional[datetime] = None
    active: bool = True
    permissions: List[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary (excluding password)"""
        data = asdict(self)
        data.pop('password_hash', None)  # Never expose password hash
        data['role'] = self.role.value
        data['created_at'] = self.created_at.isoformat()
        if self.last_login:
            data['last_login'] = self.last_login.isoformat()
        return data


@dataclass
class Device:
    """Device configuration"""
    device_id: str
    name: str
    device_type: DeviceType
    location: Dict[str, float]
    zone_id: Optional[str] = None
    model: Optional[str] = None
    firmware_version: Optional[str] = None
    ip_address: Optional[str] = None
    mac_address: Optional[str] = None
    last_seen: Optional[datetime] = None
    active: bool = True
    configuration: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['device_type'] = self.device_type.value
        if self.last_seen:
            data['last_seen'] = self.last_seen.isoformat()
        return data


@dataclass
class SystemSettings:
    """System-wide settings"""
    farm_name: str
    timezone: str
    units: str  # metric/imperial
    language: str
    
    # Irrigation settings
    default_irrigation_duration: int  # minutes
    moisture_threshold_low: float
    moisture_threshold_high: float
    
    # Communication settings
    mqtt_broker: str
    mqtt_port: int
    mqtt_username: Optional[str] = None
    
    # Notification settings
    email_enabled: bool = False
    email_smtp_server: Optional[str] = None
    email_smtp_port: int = 587
    email_username: Optional[str] = None
    
    # Data retention
    sensor_data_retention_days: int = 90
    log_retention_days: int = 30
    
    # Security settings
    session_timeout_minutes: int = 60
    password_min_length: int = 8
    require_password_change_days: int = 90


class ConfigurationManager:
    """Manages system configuration, devices, and users"""
    
    def __init__(self, config_dir: str = "config"):
        """Initialize configuration manager"""
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # Configuration files
        self.users_file = self.config_dir / "users.json"
        self.devices_file = self.config_dir / "devices.json"
        self.settings_file = self.config_dir / "settings.yaml"
        self.zones_file = self.config_dir / "zones.yaml"
        
        # In-memory storage
        self.users: Dict[str, User] = {}
        self.devices: Dict[str, Device] = {}
        self.settings: Optional[SystemSettings] = None
        self.zones_config: Dict[str, Any] = {}
        
        # Load existing configuration
        self._load_configuration()
        
        # Create default admin user if none exists
        if not self.users:
            self._create_default_admin()
    
    def _load_configuration(self):
        """Load configuration from files"""
        try:
            # Load users
            if self.users_file.exists():
                with open(self.users_file, 'r') as f:
                    users_data = json.load(f)
                    for user_data in users_data:
                        user = User(
                            user_id=user_data['user_id'],
                            username=user_data['username'],
                            email=user_data['email'],
                            role=UserRole(user_data['role']),
                            password_hash=user_data['password_hash'],
                            created_at=datetime.fromisoformat(user_data['created_at']),
                            last_login=datetime.fromisoformat(user_data['last_login']) if user_data.get('last_login') else None,
                            active=user_data.get('active', True),
                            permissions=user_data.get('permissions', [])
                        )
                        self.users[user.user_id] = user
            
            # Load devices
            if self.devices_file.exists():
                with open(self.devices_file, 'r') as f:
                    devices_data = json.load(f)
                    for device_data in devices_data:
                        device = Device(
                            device_id=device_data['device_id'],
                            name=device_data['name'],
                            device_type=DeviceType(device_data['device_type']),
                            location=device_data['location'],
                            zone_id=device_data.get('zone_id'),
                            model=device_data.get('model'),
                            firmware_version=device_data.get('firmware_version'),
                            ip_address=device_data.get('ip_address'),
                            mac_address=device_data.get('mac_address'),
                            last_seen=datetime.fromisoformat(device_data['last_seen']) if device_data.get('last_seen') else None,
                            active=device_data.get('active', True),
                            configuration=device_data.get('configuration', {})
                        )
                        self.devices[device.device_id] = device
            
            # Load system settings
            if self.settings_file.exists():
                with open(self.settings_file, 'r') as f:
                    settings_data = yaml.safe_load(f)
                    self.settings = SystemSettings(**settings_data)
            else:
                self.settings = self._create_default_settings()
            
            # Load zones configuration
            if self.zones_file.exists():
                with open(self.zones_file, 'r') as f:
                    self.zones_config = yaml.safe_load(f)
            
            logger.info("Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            # Create default configuration
            self.settings = self._create_default_settings()
    
    def _create_default_settings(self) -> SystemSettings:
        """Create default system settings"""
        return SystemSettings(
            farm_name="Smart Farm Demo",
            timezone="UTC",
            units="metric",
            language="en",
            default_irrigation_duration=30,
            moisture_threshold_low=30.0,
            moisture_threshold_high=70.0,
            mqtt_broker="localhost",
            mqtt_port=1883,
            email_enabled=False,
            sensor_data_retention_days=90,
            log_retention_days=30,
            session_timeout_minutes=60,
            password_min_length=8,
            require_password_change_days=90
        )
    
    def _create_default_admin(self):
        """Create default admin user"""
        admin_password = "admin123"  # Should be changed on first login
        password_hash = self._hash_password(admin_password)
        
        admin_user = User(
            user_id="admin_001",
            username="admin",
            email="<EMAIL>",
            role=UserRole.ADMIN,
            password_hash=password_hash,
            created_at=datetime.now(),
            permissions=["*"]  # All permissions
        )
        
        self.users[admin_user.user_id] = admin_user
        self.save_users()
        
        logger.warning("Default admin user created with password 'admin123' - CHANGE IMMEDIATELY!")
    
    def _hash_password(self, password: str) -> str:
        """Hash password with salt"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        try:
            salt, hash_hex = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash_check.hex() == hash_hex
        except Exception:
            return False
    
    def save_users(self):
        """Save users to file"""
        try:
            users_data = []
            for user in self.users.values():
                user_dict = asdict(user)
                user_dict['role'] = user.role.value
                user_dict['created_at'] = user.created_at.isoformat()
                if user.last_login:
                    user_dict['last_login'] = user.last_login.isoformat()
                users_data.append(user_dict)
            
            with open(self.users_file, 'w') as f:
                json.dump(users_data, f, indent=2)
            
            logger.info("Users configuration saved")
            
        except Exception as e:
            logger.error(f"Error saving users: {e}")
    
    def save_devices(self):
        """Save devices to file"""
        try:
            devices_data = []
            for device in self.devices.values():
                device_dict = device.to_dict()
                devices_data.append(device_dict)
            
            with open(self.devices_file, 'w') as f:
                json.dump(devices_data, f, indent=2)
            
            logger.info("Devices configuration saved")
            
        except Exception as e:
            logger.error(f"Error saving devices: {e}")
    
    def save_settings(self):
        """Save system settings to file"""
        try:
            settings_dict = asdict(self.settings)
            
            with open(self.settings_file, 'w') as f:
                yaml.dump(settings_dict, f, default_flow_style=False)
            
            logger.info("System settings saved")
            
        except Exception as e:
            logger.error(f"Error saving settings: {e}")
    
    # User Management
    def create_user(self, username: str, email: str, password: str, role: UserRole) -> str:
        """Create new user"""
        user_id = f"user_{len(self.users)}_{int(datetime.now().timestamp())}"
        password_hash = self._hash_password(password)
        
        user = User(
            user_id=user_id,
            username=username,
            email=email,
            role=role,
            password_hash=password_hash,
            created_at=datetime.now()
        )
        
        self.users[user_id] = user
        self.save_users()
        
        logger.info(f"Created user: {username} ({role.value})")
        return user_id
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate user login"""
        for user in self.users.values():
            if user.username == username and user.active:
                if self._verify_password(password, user.password_hash):
                    user.last_login = datetime.now()
                    self.save_users()
                    logger.info(f"User authenticated: {username}")
                    return user
        
        logger.warning(f"Authentication failed for user: {username}")
        return None
    
    def update_user_password(self, user_id: str, new_password: str) -> bool:
        """Update user password"""
        if user_id not in self.users:
            return False
        
        password_hash = self._hash_password(new_password)
        self.users[user_id].password_hash = password_hash
        self.save_users()
        
        logger.info(f"Password updated for user: {user_id}")
        return True
    
    def deactivate_user(self, user_id: str) -> bool:
        """Deactivate user account"""
        if user_id not in self.users:
            return False
        
        self.users[user_id].active = False
        self.save_users()
        
        logger.info(f"User deactivated: {user_id}")
        return True
    
    # Device Management
    def register_device(self, device_id: str, name: str, device_type: DeviceType, 
                       location: Dict[str, float], zone_id: str = None, **kwargs) -> bool:
        """Register new device"""
        device = Device(
            device_id=device_id,
            name=name,
            device_type=device_type,
            location=location,
            zone_id=zone_id,
            **kwargs
        )
        
        self.devices[device_id] = device
        self.save_devices()
        
        logger.info(f"Device registered: {device_id} ({device_type.value})")
        return True
    
    def update_device_status(self, device_id: str, **updates) -> bool:
        """Update device status"""
        if device_id not in self.devices:
            return False
        
        device = self.devices[device_id]
        for key, value in updates.items():
            if hasattr(device, key):
                setattr(device, key, value)
        
        device.last_seen = datetime.now()
        self.save_devices()
        
        return True
    
    def get_devices_by_zone(self, zone_id: str) -> List[Device]:
        """Get all devices in a zone"""
        return [device for device in self.devices.values() if device.zone_id == zone_id]
    
    def get_devices_by_type(self, device_type: DeviceType) -> List[Device]:
        """Get all devices of a specific type"""
        return [device for device in self.devices.values() if device.device_type == device_type]
    
    # System Settings Management
    def update_settings(self, **updates) -> bool:
        """Update system settings"""
        try:
            for key, value in updates.items():
                if hasattr(self.settings, key):
                    setattr(self.settings, key, value)
            
            self.save_settings()
            logger.info("System settings updated")
            return True
            
        except Exception as e:
            logger.error(f"Error updating settings: {e}")
            return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information"""
        return {
            "settings": asdict(self.settings),
            "users": {
                "total": len(self.users),
                "active": len([u for u in self.users.values() if u.active]),
                "by_role": {
                    role.value: len([u for u in self.users.values() if u.role == role])
                    for role in UserRole
                }
            },
            "devices": {
                "total": len(self.devices),
                "active": len([d for d in self.devices.values() if d.active]),
                "by_type": {
                    device_type.value: len([d for d in self.devices.values() if d.device_type == device_type])
                    for device_type in DeviceType
                }
            },
            "zones": len(self.zones_config.get("zones", [])),
            "last_updated": datetime.now().isoformat()
        }
    
    def export_configuration(self, filename: str = None) -> str:
        """Export complete system configuration"""
        if not filename:
            filename = f"irrigation_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        config_data = {
            "export_timestamp": datetime.now().isoformat(),
            "settings": asdict(self.settings),
            "users": [user.to_dict() for user in self.users.values()],
            "devices": [device.to_dict() for device in self.devices.values()],
            "zones": self.zones_config
        }
        
        with open(filename, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        logger.info(f"Configuration exported to {filename}")
        return filename
