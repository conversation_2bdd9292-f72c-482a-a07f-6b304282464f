"""
MQTT Communication Client for IoT Smart Irrigation System
Implements real-world MQTT patterns for IoT device communication
"""

import json
import logging
import ssl
import time
from datetime import datetime
from typing import Dict, Any, Callable, Optional
import paho.mqtt.client as mqtt
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class MQTTClient:
    """
    MQTT client for IoT device communication
    Implements industry-standard MQTT patterns for reliable IoT messaging
    """
    
    def __init__(self, 
                 broker_host: str = "localhost",
                 broker_port: int = 1883,
                 client_id: str = None,
                 username: str = None,
                 password: str = None,
                 use_tls: bool = False):
        """
        Initialize MQTT client with connection parameters
        
        Args:
            broker_host: MQTT broker hostname
            broker_port: MQTT broker port
            client_id: Unique client identifier
            username: MQTT username for authentication
            password: MQTT password for authentication
            use_tls: Enable TLS encryption
        """
        self.broker_host = broker_host
        self.broker_port = broker_port
        self.client_id = client_id or f"irrigation_client_{int(time.time())}"
        self.username = username
        self.password = password
        self.use_tls = use_tls
        
        # MQTT client setup
        self.client = mqtt.Client(client_id=self.client_id)
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        self.client.on_message = self._on_message
        self.client.on_publish = self._on_publish
        self.client.on_subscribe = self._on_subscribe
        
        # Connection state
        self.is_connected = False
        self.connection_attempts = 0
        self.max_reconnect_attempts = 10
        
        # Message handling
        self.message_handlers: Dict[str, Callable] = {}
        self.subscription_callbacks: Dict[str, Callable] = {}
        
        # Quality of Service settings
        self.default_qos = 1  # At least once delivery
        self.retain_status_messages = True
        
        # Statistics
        self.messages_sent = 0
        self.messages_received = 0
        self.connection_time = None
        
        # Setup authentication and TLS
        self._setup_authentication()
        self._setup_tls()
    
    def _setup_authentication(self):
        """Setup MQTT authentication"""
        if self.username and self.password:
            self.client.username_pw_set(self.username, self.password)
            logger.info("MQTT authentication configured")
    
    def _setup_tls(self):
        """Setup TLS encryption"""
        if self.use_tls:
            context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE  # For development
            self.client.tls_set_context(context)
            logger.info("MQTT TLS encryption enabled")
    
    def _on_connect(self, client, userdata, flags, rc):
        """Callback for successful MQTT connection"""
        if rc == 0:
            self.is_connected = True
            self.connection_time = datetime.now()
            self.connection_attempts = 0
            logger.info(f"Connected to MQTT broker {self.broker_host}:{self.broker_port}")
            
            # Subscribe to control topics
            self._subscribe_to_control_topics()
            
            # Publish connection status
            self._publish_status("online")
            
        else:
            logger.error(f"Failed to connect to MQTT broker. Return code: {rc}")
            self.is_connected = False
    
    def _on_disconnect(self, client, userdata, rc):
        """Callback for MQTT disconnection"""
        self.is_connected = False
        logger.warning(f"Disconnected from MQTT broker. Return code: {rc}")
        
        if rc != 0:  # Unexpected disconnection
            self._attempt_reconnection()
    
    def _on_message(self, client, userdata, msg):
        """Callback for received MQTT messages"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            
            logger.debug(f"Received message on topic {topic}: {payload}")
            self.messages_received += 1
            
            # Try to parse JSON payload
            try:
                message_data = json.loads(payload)
            except json.JSONDecodeError:
                message_data = {"raw_payload": payload}
            
            # Route message to appropriate handler
            self._route_message(topic, message_data)
            
        except Exception as e:
            logger.error(f"Error processing received message: {e}")
    
    def _on_publish(self, client, userdata, mid):
        """Callback for successful message publication"""
        logger.debug(f"Message published successfully. Message ID: {mid}")
        self.messages_sent += 1
    
    def _on_subscribe(self, client, userdata, mid, granted_qos):
        """Callback for successful subscription"""
        logger.debug(f"Subscription successful. Message ID: {mid}, QoS: {granted_qos}")
    
    def _subscribe_to_control_topics(self):
        """Subscribe to control and command topics"""
        control_topics = [
            f"irrigation/{self.client_id}/commands/+",
            f"irrigation/{self.client_id}/config/+",
            "irrigation/broadcast/+",
            "irrigation/system/time"
        ]
        
        for topic in control_topics:
            self.client.subscribe(topic, qos=self.default_qos)
            logger.info(f"Subscribed to topic: {topic}")
    
    def _route_message(self, topic: str, message_data: Dict[str, Any]):
        """Route received messages to appropriate handlers"""
        # Check for specific topic handlers
        if topic in self.subscription_callbacks:
            try:
                self.subscription_callbacks[topic](topic, message_data)
            except Exception as e:
                logger.error(f"Error in topic handler for {topic}: {e}")
            return
        
        # Check for pattern-based handlers
        for pattern, handler in self.message_handlers.items():
            if self._topic_matches_pattern(topic, pattern):
                try:
                    handler(topic, message_data)
                except Exception as e:
                    logger.error(f"Error in pattern handler for {pattern}: {e}")
                return
        
        # Default handling for unhandled messages
        self._handle_unhandled_message(topic, message_data)
    
    def _topic_matches_pattern(self, topic: str, pattern: str) -> bool:
        """Check if topic matches MQTT pattern (with + and # wildcards)"""
        topic_parts = topic.split('/')
        pattern_parts = pattern.split('/')
        
        if len(pattern_parts) > len(topic_parts):
            return False
        
        for i, pattern_part in enumerate(pattern_parts):
            if pattern_part == '#':
                return True  # # matches everything after
            elif pattern_part == '+':
                continue  # + matches any single level
            elif i >= len(topic_parts) or pattern_part != topic_parts[i]:
                return False
        
        return len(topic_parts) == len(pattern_parts)
    
    def _handle_unhandled_message(self, topic: str, message_data: Dict[str, Any]):
        """Handle messages that don't match any registered handlers"""
        logger.info(f"Unhandled message on topic {topic}: {message_data}")
    
    def _attempt_reconnection(self):
        """Attempt to reconnect to MQTT broker"""
        if self.connection_attempts < self.max_reconnect_attempts:
            self.connection_attempts += 1
            wait_time = min(60, 2 ** self.connection_attempts)  # Exponential backoff
            
            logger.info(f"Attempting reconnection {self.connection_attempts}/{self.max_reconnect_attempts} in {wait_time} seconds")
            time.sleep(wait_time)
            
            try:
                self.client.reconnect()
            except Exception as e:
                logger.error(f"Reconnection attempt failed: {e}")
        else:
            logger.error("Maximum reconnection attempts reached")
    
    def connect(self) -> bool:
        """Connect to MQTT broker"""
        try:
            logger.info(f"Connecting to MQTT broker {self.broker_host}:{self.broker_port}")
            self.client.connect(self.broker_host, self.broker_port, keepalive=60)
            self.client.loop_start()  # Start background thread
            
            # Wait for connection
            timeout = 10  # seconds
            start_time = time.time()
            while not self.is_connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            return self.is_connected
            
        except Exception as e:
            logger.error(f"Error connecting to MQTT broker: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MQTT broker"""
        if self.is_connected:
            self._publish_status("offline")
            self.client.loop_stop()
            self.client.disconnect()
            logger.info("Disconnected from MQTT broker")
    
    def publish(self, 
                topic: str, 
                payload: Dict[str, Any], 
                qos: int = None, 
                retain: bool = False) -> bool:
        """
        Publish message to MQTT topic
        
        Args:
            topic: MQTT topic to publish to
            payload: Message payload (will be JSON encoded)
            qos: Quality of Service level (0, 1, or 2)
            retain: Whether to retain the message
        
        Returns:
            True if message was queued for sending, False otherwise
        """
        if not self.is_connected:
            logger.error("Cannot publish: not connected to MQTT broker")
            return False
        
        try:
            qos = qos if qos is not None else self.default_qos
            json_payload = json.dumps(payload, default=str)
            
            result = self.client.publish(topic, json_payload, qos=qos, retain=retain)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                logger.debug(f"Published to {topic}: {json_payload}")
                return True
            else:
                logger.error(f"Failed to publish to {topic}. Return code: {result.rc}")
                return False
                
        except Exception as e:
            logger.error(f"Error publishing to {topic}: {e}")
            return False
    
    def subscribe(self, topic: str, callback: Callable, qos: int = None):
        """
        Subscribe to MQTT topic with callback
        
        Args:
            topic: MQTT topic to subscribe to
            callback: Function to call when message received
            qos: Quality of Service level
        """
        qos = qos if qos is not None else self.default_qos
        
        self.subscription_callbacks[topic] = callback
        self.client.subscribe(topic, qos=qos)
        logger.info(f"Subscribed to topic: {topic}")
    
    def add_message_handler(self, topic_pattern: str, handler: Callable):
        """
        Add message handler for topic pattern
        
        Args:
            topic_pattern: MQTT topic pattern (can include + and # wildcards)
            handler: Function to handle matching messages
        """
        self.message_handlers[topic_pattern] = handler
        logger.info(f"Added message handler for pattern: {topic_pattern}")
    
    def _publish_status(self, status: str):
        """Publish device status"""
        status_topic = f"irrigation/{self.client_id}/status"
        status_payload = {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "client_id": self.client_id
        }
        
        self.publish(status_topic, status_payload, retain=self.retain_status_messages)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        uptime = None
        if self.connection_time:
            uptime = (datetime.now() - self.connection_time).total_seconds()
        
        return {
            "is_connected": self.is_connected,
            "broker_host": self.broker_host,
            "broker_port": self.broker_port,
            "client_id": self.client_id,
            "connection_attempts": self.connection_attempts,
            "messages_sent": self.messages_sent,
            "messages_received": self.messages_received,
            "uptime_seconds": uptime,
            "connection_time": self.connection_time.isoformat() if self.connection_time else None
        }
