#!/bin/bash

# IoT Smart Irrigation System - Startup Script
# This script sets up and starts the complete IoT irrigation system

set -e

echo "🌱 Starting IoT Smart Irrigation System..."
echo "=========================================="

# Check if <PERSON>er and <PERSON>er Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p deployment/mosquitto/{config,data,log}
mkdir -p deployment/mongodb/init
mkdir -p deployment/grafana/{provisioning,dashboards}
mkdir -p deployment/nginx/ssl
mkdir -p deployment/prometheus
mkdir -p logs
mkdir -p data
mkdir -p config

# Create MQTT configuration
echo "🔧 Setting up MQTT configuration..."
cat > deployment/mosquitto/config/mosquitto.conf << EOF
# MQTT Broker Configuration
listener 1883
allow_anonymous true
persistence true
persistence_location /mosquitto/data/
log_dest file /mosquitto/log/mosquitto.log
log_type error
log_type warning
log_type notice
log_type information

# WebSocket support
listener 9001
protocol websockets
EOF

# Create MongoDB initialization script
echo "🔧 Setting up MongoDB initialization..."
cat > deployment/mongodb/init/init-irrigation.js << EOF
// MongoDB initialization for irrigation system
db = db.getSiblingDB('irrigation_system');

// Create collections
db.createCollection('sensors');
db.createCollection('devices');
db.createCollection('alerts');
db.createCollection('users');

// Create indexes
db.sensors.createIndex({ "sensor_id": 1 }, { unique: true });
db.sensors.createIndex({ "timestamp": -1 });
db.devices.createIndex({ "device_id": 1 }, { unique: true });
db.alerts.createIndex({ "timestamp": -1 });
db.users.createIndex({ "username": 1 }, { unique: true });

print("Irrigation database initialized successfully");
EOF

# Create Prometheus configuration
echo "🔧 Setting up Prometheus configuration..."
cat > deployment/prometheus/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'irrigation-app'
    static_configs:
      - targets: ['irrigation_app:8000']
    metrics_path: '/metrics'

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node_exporter:9100']

  - job_name: 'influxdb'
    static_configs:
      - targets: ['influxdb:8086']
EOF

# Create Nginx configuration
echo "🔧 Setting up Nginx configuration..."
cat > deployment/nginx/nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream irrigation_app {
        server irrigation_app:8000;
    }

    upstream grafana {
        server grafana:3000;
    }

    server {
        listen 80;
        server_name localhost;

        # Main application
        location / {
            proxy_pass http://irrigation_app;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # WebSocket support
        location /ws {
            proxy_pass http://irrigation_app;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # Grafana dashboards
        location /grafana/ {
            proxy_pass http://grafana/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
    }
}
EOF

# Create environment file
echo "🔧 Creating environment configuration..."
cat > .env << EOF
# IoT Smart Irrigation System Environment Configuration

# Database Configuration
MONGODB_URL=******************************************************************************
INFLUXDB_URL=http://influxdb:8086
INFLUXDB_TOKEN=irrigation-super-secret-auth-token
INFLUXDB_ORG=irrigation-org
INFLUXDB_BUCKET=sensor-data

# Redis Configuration
REDIS_HOST=redis
REDIS_PASSWORD=redis_password_123

# MQTT Configuration
MQTT_BROKER=mosquitto
MQTT_PORT=1883

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO
TIMEZONE=UTC

# Security (Change these in production!)
SECRET_KEY=your-super-secret-key-change-this
JWT_SECRET=your-jwt-secret-change-this

# Email Configuration (Optional)
EMAIL_ENABLED=false
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
EOF

# Pull Docker images
echo "📦 Pulling Docker images..."
docker-compose pull

# Build application image
echo "🔨 Building application image..."
docker-compose build

# Start services
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."
services=("mosquitto" "mongodb" "influxdb" "redis" "irrigation_app")

for service in "${services[@]}"; do
    if docker-compose ps | grep -q "$service.*Up"; then
        echo "✅ $service is running"
    else
        echo "❌ $service failed to start"
        docker-compose logs "$service"
    fi
done

# Display access information
echo ""
echo "🎉 IoT Smart Irrigation System is now running!"
echo "=============================================="
echo ""
echo "📊 Access Points:"
echo "  • Main Dashboard:    http://localhost"
echo "  • Mobile Interface:  http://localhost/mobile/mobile_app.html"
echo "  • API Documentation: http://localhost/docs"
echo "  • Grafana:          http://localhost/grafana (admin/grafana_admin_123)"
echo "  • Prometheus:       http://localhost:9090"
echo ""
echo "🔧 Management:"
echo "  • MQTT Broker:      localhost:1883"
echo "  • MongoDB:          localhost:27017"
echo "  • InfluxDB:         localhost:8086"
echo "  • Redis:            localhost:6379"
echo ""
echo "📝 Default Login:"
echo "  • Username: admin"
echo "  • Password: admin123"
echo "  • ⚠️  CHANGE PASSWORD IMMEDIATELY!"
echo ""
echo "🛑 To stop the system: docker-compose down"
echo "📋 To view logs: docker-compose logs -f"
echo "🔄 To restart: docker-compose restart"
echo ""
echo "📖 For more information, see README.md"
EOF

chmod +x deployment/start.sh
